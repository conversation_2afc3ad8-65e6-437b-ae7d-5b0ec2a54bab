<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    import {
        capitalizeFirstWord,
        commaSeparateNumber,
        debounce,
        formatDateUI,
        getDateDifference,
        getFutureDate,
        getPastDate,
        separateSnakecase,
        showErrorToast,
    } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import Loader from "$lib/common/components/Loader.svelte";
    import { TABLE_FIELDS, type IRawMaterialStockInDetails } from "../models/IRawMaterialStock";
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
    import SearchDropWithCheckbox from "$lib/common/components/SearchDropWithCheckbox.svelte";
    import { downloadAllCsv, downloadCsv } from "../utils/RawMaterialStockUtils";
    import headerPattern from "$lib/common/assets/pattern/table-header.png";
    import theme from "$lib/common/assets/theme";

    export let paginationData: PaginatedDataWrapper<IRawMaterialStockInDetails>;
    export let searchTerm: string = "";
    export let showFilters: boolean = false;
    export let onSearchClear: () => void = () => {};
    export let selectedRowsMap: Map<number, IRawMaterialStockInDetails> = new Map();
    export let withoutStorageLocation = false;

    export let onDateFilterChange: (
        filter: {
            startDate: string;
            endDate: string;
            currentFilter: "today" | "week" | "month";
        } | null
    ) => void;

    export let currentFilter: "today" | "week" | "month" | undefined;

    let badLogic = "";
    let isLoading: boolean = false;
    let isSearching: boolean = false;
    let filteredData: IRawMaterialStockInDetails[] = [];
    let allCheckBoxArray: Array<string> = Object.values(TABLE_FIELDS);
    let checkedCheckBoxArray: Array<string>;
    let selectAll = false;
    let debounceSearch = debounce(async (e: any) => {
        if (searchTerm.trim().length === 0 && !currentFilter) {
            onSearchClear();
            return;
        }

        if (e.target.value.trim().length > 2) {
            isSearching = true;

            let result;

            if (withoutStorageLocation) {
                result =
                    await PresenterProvider.rawMaterialStockPresenter.searchStockInByTextWithoutStorage(
                        e.target.value.trim()
                    );
            } else {
                paginationData.searchText = e.target.value.trim();

                result = await PresenterProvider.rawMaterialStockPresenter.getStockInEntries(
                    paginationData.pagination.currentPage,
                    paginationData.pageSize,
                    paginationData.searchText
                );
            }
            if (!result.success) {
                return showErrorToast(result.message);
            } else {
                filteredData = result.data.data;

                paginationData.pagination = result.data;

                isSearching = false;
            }
        }
    }, 300);

    function toggleSelectAll() {
        if (selectAll) {
            filteredData.forEach((rmData) => {
                selectedRowsMap.set(rmData.id, rmData);
            });
        } else {
            selectedRowsMap.clear();
            selectAll = false;
        }
        selectedRowsMap = selectedRowsMap;
    }

    function toggleRowSelection(row: IRawMaterialStockInDetails, id: number) {
        if (selectedRowsMap.has(id)) {
            selectedRowsMap.delete(id);
        } else {
            selectedRowsMap.set(id, row);
        }
    }
    const getDateOrMonthFormat = (num: number) => {
        return (num > 9 ? num : "0" + num).toString();
    };
    const exportAllDate: {
        startDate: Date;
        endDate: Date;
        loading: boolean;
        exportAllLoading: boolean;
    } = {
        startDate: getPastDate(new Date(), 365),
        endDate: new Date(),
        loading: false,
        exportAllLoading: false,
    };
    const weekFilter = () => {
        const now = new Date();
        let currentDayOfWeek = now.getDay();

        const diffToSunday = now.getDate() - currentDayOfWeek;
        const startDate = new Date(now.setDate(diffToSunday));
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);

        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        return {
            startDate:
                startDate.getFullYear() +
                "-" +
                getDateOrMonthFormat(startDate.getMonth() + 1) +
                "-" +
                getDateOrMonthFormat(startDate.getDate()),
            endDate:
                endDate.getFullYear() +
                "-" +
                getDateOrMonthFormat(endDate.getMonth() + 1) +
                "-" +
                getDateOrMonthFormat(endDate.getDate() + 1),
        };
    };

    onMount(() => {
        filteredData = paginationData.pagination.data;
        console.log(filteredData.length + 1);
        searchTerm = paginationData.searchText ?? "";
    });
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
    <!-- svelte-ignore a11y_no_static_element_interactions -->
{:else}
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_static_element_interactions -->
    <div
        on:click={() => {
            badLogic = "";
        }}
    >
        <h1
            class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
        >
            Stock In Details
        </h1>
        <div>
            <div class="flex my-3 px-1 flex-col md:flex-row items-center gap-4 justify-end">
                <p class="text-sm italic text-red-500">* Date Range cannot be more than 1 year</p>
            </div>

            <div class="flex my-3 px-1 flex-col md:flex-row items-center gap-4 justify-end">
                <!-- Start Date Input -->
                <div class="flex flex-col md:flex-row md:items-center gap-2">
                    <label for="start-date" class="font-medium text-gray-700">Start Date</label>
                    <input
                        type="date"
                        id="start-date"
                        class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                        value={exportAllDate.startDate.toISOString().split("T")[0]}
                        on:input={(e) => {
                            //  @ts-ignore
                            const dd = new Date(e.target.value);
                            const number = getDateDifference(dd, exportAllDate.endDate);
                            if (number > 365) {
                                exportAllDate.endDate = getFutureDate(dd, 365);
                                exportAllDate.startDate = dd;
                            } else if (number < 1) {
                                exportAllDate.startDate = dd;
                                exportAllDate.endDate = dd;
                            }
                        }}
                    />
                </div>

                <!-- End Date Input -->
                <div class="flex flex-col md:flex-row md:items-center gap-2">
                    <label for="end-date" class="font-medium text-gray-700">End Date</label>
                    <input
                        type="date"
                        id="end-date"
                        class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                        value={exportAllDate.endDate.toISOString().split("T")[0]}
                        on:input={(e) => {
                            //  @ts-ignore
                            const dd = new Date(e.target.value);
                            const number = getDateDifference(exportAllDate.startDate, dd);

                            if (number > 365) {
                                exportAllDate.startDate = getPastDate(dd, 365);
                                exportAllDate.endDate = dd;
                            } else if (number < 1) {
                                exportAllDate.endDate = dd;
                                exportAllDate.startDate = getPastDate(dd, 1);
                            }
                        }}
                    />
                </div>

                <!-- Export Button -->
                <div class="h-full flex items-center">
                    <button
                        id="dropdownActionButton"
                        data-dropdown-toggle="dropdownAction"
                        class="inline-flex items-center justify-center rounded-lg border border-gray-300 text-gray-600 px-4 py-2 text-sm font-medium hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-red-300"
                        type="button"
                        disabled={exportAllDate.exportAllLoading}
                        on:click={async () => {
                            exportAllDate.exportAllLoading = true;
                            await downloadAllCsv(
                                exportAllDate.startDate,
                                exportAllDate.endDate,
                                searchTerm
                            );
                            exportAllDate.exportAllLoading = false;
                        }}
                    >
                        {#if exportAllDate.exportAllLoading}
                            <div class="flex gap-2 items-center justify-center">
                                Loading <Spinner class="size-5" />
                            </div>
                        {:else}
                            Export All
                        {/if}
                    </button>
                </div>
            </div>
        </div>
        <div class="overflow-hidden rounded-xl border border-foreground/20 bg-background shadow-lg">
            <div
                class="flex justify-between rounded-t-xl border-b border-foreground/20 bg-cover bg-center bg-repeat p-6"
                style={`background: ${theme.tableHeader};`}
            >
                <label for="table-search" class="sr-only">Search</label>

                <div class="relative w-full md:w-80">
                    <div
                        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 z-10"
                    >
                        <svg
                            class="h-4 w-4 text-black/70"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            ></path>
                        </svg>
                    </div>
                    <input
                        type="text"
                        id="raw-material-stock-in-search"
                        bind:value={searchTerm}
                        on:input={debounceSearch}
                        class="w-full rounded-lg border border-accent/20 bg-background backdrop-blur-sm px-4 py-2.5 pl-10 text-sm text-black placeholder:text-black/50 focus:border-accent focus:outline-none focus:ring-2 focus:ring-accent/10 transition-all duration-300 relative z-0"
                        placeholder="Search by raw material name, PO No. or Invoice No."
                    />
                    {#if isSearching}
                        <div class="absolute right-3 top-1/2 -translate-y-1/2 z-10">
                            <Loader />
                        </div>
                    {/if}
                </div>
                <div class="flex items-center space-x-3">
                    <div
                        on:click={(e) => {
                            e.stopPropagation();
                            badLogic = "1";
                        }}
                    >
                        <SearchDropWithCheckbox
                            searchTerm={badLogic}
                            bind:checkedCheckBoxArray
                            label={"Table Fields"}
                            isLabel={false}
                            filteredData={allCheckBoxArray}
                        />
                    </div>
                    <button
                        id="dropdownActionButton"
                        data-dropdown-toggle="dropdownAction"
                        class="inline-flex items-center rounded-lg bg-background px-4 py-2.5 text-sm font-semibold hover:bg-background/90 text-accent shadow-md transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-accent/20"
                        type="button"
                        on:click={() => {
                            goto("/admin/purchase-invoices/add");
                        }}
                    >
                        <svg
                            class="mr-2 h-4 w-4 transition-transform duration-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                            ></path>
                        </svg>
                        Add New
                    </button>
                    <!-- Export Button -->
                    <button
                        id="dropdownActionButton"
                        data-dropdown-toggle="dropdownAction"
                        class="inline-flex items-center justify-center rounded-lg bg-background px-4 py-2.5 text-sm font-semibold hover:bg-background/90 text-accent shadow-md transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-accent/20"
                        type="button"
                        disabled={exportAllDate.loading}
                        on:click={async () => {
                            exportAllDate.loading = true;
                            await downloadCsv(
                                [...selectedRowsMap.values()],
                                "Raw Material Stock-In"
                            );
                            exportAllDate.loading = false;
                        }}
                    >
                        {#if exportAllDate.loading}
                            <div class="flex gap-2 items-center justify-center">
                                Loading <Spinner class="size-5" />
                            </div>
                        {:else}
                            <svg
                                class="mr-2 h-4 w-4 transition-transform duration-300"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                ></path>
                            </svg>
                            Export
                        {/if}
                    </button>
                </div>
            </div>

            {#if showFilters}
                <div class="my-6">
                    <span class="mx-3">Filter By Date Range</span>

                    <CustomButton
                        title="Today"
                        cssClass={currentFilter === "today"
                            ? "!bg-accent !text-background"
                            : "!bg-foreground !text-accent !border-accent/20"}
                        onClick={() => {
                            if (currentFilter === "today") {
                                onDateFilterChange(null);
                                return;
                            }
                            const today = new Date();

                            onDateFilterChange({
                                startDate:
                                    today.getFullYear() +
                                    "-" +
                                    getDateOrMonthFormat(today.getMonth() + 1) +
                                    "-" +
                                    getDateOrMonthFormat(today.getDate()),
                                endDate:
                                    today.getFullYear() +
                                    "-" +
                                    getDateOrMonthFormat(today.getMonth() + 1) +
                                    "-" +
                                    getDateOrMonthFormat(today.getDate() + 1),
                                currentFilter: "today",
                            });
                        }}
                    />

                    <CustomButton
                        title="This Week"
                        cssClass={currentFilter === "week"
                            ? "!bg-accent !text-background"
                            : "!bg-foreground !text-accent !border-accent/20"}
                        onClick={() => {
                            if (currentFilter === "week") {
                                onDateFilterChange(null);
                                return;
                            }

                            onDateFilterChange({
                                ...weekFilter(),
                                currentFilter: "week",
                            });
                        }}
                    />

                    <CustomButton
                        title="This Month"
                        cssClass={currentFilter === "month"
                            ? "!bg-accent !text-background"
                            : "!bg-foreground !text-accent !border-accent/20"}
                        onClick={() => {
                            if (currentFilter === "month") {
                                onDateFilterChange(null);
                                return;
                            }
                            const today = new Date();

                            onDateFilterChange({
                                startDate:
                                    today.getFullYear() +
                                    "-" +
                                    getDateOrMonthFormat(today.getMonth() + 1) +
                                    "-" +
                                    "01",
                                endDate:
                                    today.getFullYear() +
                                    "-" +
                                    getDateOrMonthFormat(today.getMonth() + 1) +
                                    "-" +
                                    31,
                                currentFilter: "month",
                            });
                        }}
                    />
                </div>
            {/if}
            {#if isSearching}
                <div class="flex h-[30rem] w-full items-center justify-center">
                    <Loader />
                </div>
            {:else}
                <div class="max-h-[50vh] overflow-y-auto">
                    <table
                        class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right"
                    >
                        <thead class="sticky top-0 bg-foreground text-accent shadow-md">
                            <tr class="w-fit">
                                <th class="px-4 py-3">
                                    <input
                                        type="checkbox"
                                        bind:checked={selectAll}
                                        on:change={toggleSelectAll}
                                    />
                                </th>
                                <th
                                    scope="col"
                                    class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                                >
                                    Sr. No.
                                </th>
                                {#if checkedCheckBoxArray.length > 0}
                                    {#each checkedCheckBoxArray as checkboxData}
                                        <th
                                            scope="col"
                                            class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                                        >
                                            {separateSnakecase(checkboxData)}
                                        </th>
                                    {/each}
                                {/if}
                            </tr>
                        </thead>
                        <tbody>
                            {#each filteredData as row, index}
                                <tr
                                    class="bg-background text-black transition-all duration-300 hover:bg-foreground/20 hover:shadow-sm ease-in-out"
                                >
                                    <td class="px-4 py-4">
                                        <input
                                            type="checkbox"
                                            checked={selectedRowsMap.has(row.id)}
                                            on:change={() => toggleRowSelection(row, row.id)}
                                        />
                                    </td>
                                    <td class="px-6 py-4">
                                        <a
                                            href={row.storageLocation != null
                                                ? "#"
                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                  row.id}
                                            class="block h-full w-full whitespace-nowrap font-medium"
                                        >
                                            {(paginationData.pagination.currentPage - 1) *
                                                paginationData.pageSize +
                                                index +
                                                1}
                                        </a>
                                    </td>
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.PO_NUMBER)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.poNumber}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.INVOICE_NUMBER)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.purchaseInvoiceNumber}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.PURCHASED_BY)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.purchasedBy}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.RAW_MATERIAL)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {capitalizeFirstWord(row.rawMaterial)}
                                            </a>
                                        </td>
                                    {/if}

                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.CATEGORY)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {capitalizeFirstWord(row.categoryName)}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.SUPPLIER)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {capitalizeFirstWord(row.supplier)}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.TOTAL_QTY)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.totalQty}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.EXCESS_QTY)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.excessQty}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.REPLACEABLE_QTY)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.replaceableQty}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.REJECTED_QTY)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.rejectedQty}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.HOLD_QTY)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.holdQty}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.STOCK_IN_QTY)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {(
                                                    row.totalQty -
                                                    (row.rejectedQty + row.holdQty)
                                                ).toFixed(2)}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.UNIT_PRICE)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {commaSeparateNumber(row.price.toString())}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.PRICE)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {commaSeparateNumber(
                                                    (row.totalQty * row.price).toFixed(2)
                                                )}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.STORAGE_LOCATION)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.storageLocation}
                                            </a>
                                        </td>
                                    {/if}

                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.FACTORY_GATE)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {row.factoryGate}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.RECEIVED_ON)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {formatDateUI(row.receivedAt, false)}
                                            </a>
                                        </td>
                                    {/if}
                                    {#if checkedCheckBoxArray.includes(TABLE_FIELDS.CREATED_AT)}
                                        <td class="px-6 py-4">
                                            <a
                                                href={row.storageLocation != null
                                                    ? "#"
                                                    : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                      row.id}
                                                class="block h-full w-full whitespace-nowrap font-medium"
                                            >
                                                {formatDateUI(row.createdAt)}
                                            </a>
                                        </td>
                                    {/if}
                                </tr>
                            {/each}
                            {#if filteredData.length === 0}
                                <tr>
                                    <td
                                        colspan={checkedCheckBoxArray.length + 2}
                                        class="h-80 text-center text-black/60"
                                    >
                                        <div
                                            class="flex flex-col items-center justify-center space-y-3"
                                        >
                                            <div class="relative">
                                                <div
                                                    class="absolute inset-0 bg-gradient-to-r from-accent/20 to-accent/10 rounded-full blur-lg"
                                                ></div>
                                                <svg
                                                    class="relative h-12 w-12 text-accent/60"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        stroke-width="1.5"
                                                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                                                    ></path>
                                                </svg>
                                            </div>
                                            <div>
                                                <p class="text-base font-semibold">
                                                    No stock in entries found
                                                </p>
                                                <p class="text-sm text-black/40">
                                                    {searchTerm.trim().length > 0
                                                        ? "Try adjusting your search criteria"
                                                        : "No raw material stock in entries available"}
                                                </p>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {/if}
                        </tbody>
                    </table>
                </div>
            {/if}
        </div>
        <PaginationButtons {paginationData} />
    </div>
{/if}
