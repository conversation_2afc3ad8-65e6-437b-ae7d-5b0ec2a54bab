<script lang="ts">
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { CloseButton, Input, Label } from "flowbite-svelte";
    import type {
        IPurchaseOrder,
        IPurchaseOrderItems,
        PurchaseItemDetails,
    } from "../models/IPurchaseOrder";
    import { getEmptyPurchaseOrderItems } from "../utils/purchase-order-utils";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import SupplierRawMaterialSearch from "$lib/purchase_invoice/components/SupplierRawMaterialSearch.svelte";
    import {
        capitalizeFirstWord,
        formatDateToInput,
        formatDateUI,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { type IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
    import { RepoProvider } from "$lib/RepoProvider";
    import { goto } from "$app/navigation";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { onMount } from "svelte";

    import jsPDF from "jspdf";
    import { LOGO_URL } from "$lib/common/models/constants";
    import type { IDepartment } from "$lib/department/models/IDepartment";
    import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
    import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
    import theme from "$lib/common/assets/theme";
    import DepartmentDropdown from "$lib/department/components/DepartmentDropdown.svelte";

    export let isEditCase: boolean = false;
    export let formData: IPurchaseOrder = getEmptyPurchaseOrderItems();

    let selectedSupplier: ISupplier | null = null;
    let selectedFromDepartment: IDepartment | null = null;
    let selectedToDepartment: IDepartment | null = null;
    let selectedItemsMap: Map<string, IPurchaseOrderItems> = new Map();
    let isLoading: boolean = true;
    let oldRawMaterialsIds: number[] = [];
    let validationErrors: Map<string, string> = new Map();

    // Department data and loading state
    let departmentData: IDepartment[] = [];
    let isDepartmentLoading: boolean = true;

    // Fetch department data
    const fetchDepartments = async () => {
        try {
            isDepartmentLoading = true;
            const res = await RepoProvider.departmentRepo.getAll(1, 100); // Fetch first 100 departments
            if (res.success) {
                departmentData = res.data.data.map((dept) => ({
                    id: Number(dept.id),
                    name: dept.name,
                    status: dept.status,
                    createdAt: dept.createdAt,
                    updatedAt: null,
                    deletedAt: null,
                    createdBy: dept.createdBy,
                    updatedBy: null,
                    deletedBy: null,
                }));
            } else {
                showErrorToast("Failed to fetch departments: " + res.message);
                departmentData = [];
            }
        } catch (error) {
            console.error("Error fetching departments:", error);
            showErrorToast("Failed to fetch departments");
            departmentData = [];
        } finally {
            isDepartmentLoading = false;
        }
    };

    const handleTotalQty = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                formData.items[index].qty = 0;
                return;
            }
            if (e.target.value < 0) {
                e.target.value = 0;
            }
            formData.items[index].qty = parseFloat(e.target.value);
            console.log(formData.items[index].qty);
        } catch (error) {
            formData.items[index].qty = 0;
        }
    };
    const onRawMaterialSelected = (
        data: IRawMaterialVariation,
        item: PurchaseItemDetails,
        index: number
    ) => {
        if (data) {
            if (oldRawMaterialsIds.includes(data.id)) {
                showErrorToast(data.name + " is already added");
                return;
            }

            const gstPercentage = data.gstPercentage;

            item.item = data;
            selectedItemsMap.set(index.toString(), {
                id: data.id,
                qty: 0,
                name: data.name,
            });
        } else {
            oldRawMaterialsIds = oldRawMaterialsIds.filter((id) => id !== item.item.id);

            selectedItemsMap.delete(index.toString());
            formData.items.splice(index, 1);
            // Recreate selected item map
            selectedItemsMap = new Map();
            formData.items.forEach((item, idx) => {
                selectedItemsMap.set(idx.toString(), {
                    id: item.item.id,
                    qty: item.qty,
                    name: item.item.name,
                });
            });
        }

        formData = formData;
        selectedItemsMap = selectedItemsMap;
        console.log(selectedItemsMap, "selecteditem map");
        console.log(formData, "formData");
    };
    const getSelected = (index: number) => {
        const item = selectedItemsMap.get(index.toString());
        console.log(item);
        return item
            ? ({
                  id: item.id,
                  qty: item.qty,
                  name: item.name,
              } as unknown as IRawMaterialVariation)
            : null;
    };

    const onSubmitHandler = async () => {
        isLoading = true;
        validationErrors = PresenterProvider.purchaseOrderPresenter.onValidate(formData);
        if (validationErrors.size !== 0) {
            showErrorToast(validationErrors.get(validationErrors.keys().next().value!));
            isLoading = false;
            return;
        }
        if (!isEditCase) {
            const res = await RepoProvider.purchaseOrderRepo.create(formData);
            if (res.success) {
                showSuccessToast("Demand Slip Saved Successfully!");
                await goto("/admin/purchase-orders");
            } else {
                showErrorToast(res.message);
            }
        } else {
            const res = await RepoProvider.purchaseOrderRepo.update(formData);
            if (res.success) {
                showSuccessToast("Demand Slip Updated Successfully!");
                await goto("/admin/purchase-orders");
            } else {
                showErrorToast(res.message);
            }
        }
        isLoading = false;
    };

    $: {
        selectedItemsMap;
    }

    onMount(async () => {
        // Fetch departments first
        await fetchDepartments();
        console.log(formData.supplier);
        if (formData.supplier.id > 0) {
            selectedSupplier = formData.supplier;
            if (formData.id) {
                formData.id = formData.id;
                if (formData.id > 0) {
                    formData.expectedDate = new Date(formData.expectedDate);
                }
            }
        }

        // Initialize department selections
        if (formData.fromDepartment) {
            selectedFromDepartment = formData.fromDepartment;
        } else if (!isEditCase && departmentData.length > 0) {
            // Set default "from department" to "store" for new purchase orders
            const storeDepartment = departmentData.find(
                (dept) => dept.name.toLowerCase() === "store"
            );
            if (storeDepartment) {
                selectedFromDepartment = storeDepartment;
                formData.fromDepartment = storeDepartment;
            }
        }

        if (formData.toDepartment) {
            selectedToDepartment = formData.toDepartment;
        } else if (!isEditCase && departmentData.length > 0) {
            // Set default "to department" to "purchase" for new purchase orders
            const purchaseDepartment = departmentData.find(
                (dept) => dept.name.toLowerCase() === "purchase"
            );
            if (purchaseDepartment) {
                selectedToDepartment = purchaseDepartment;
                formData.toDepartment = purchaseDepartment;
            }
        }

        let index = 0;
        for (const item of formData.items) {
            oldRawMaterialsIds.push(item.item.id);

            selectedItemsMap.set(index.toString(), {
                id: item.item.id,
                qty: item.qty,
                name: item.item.name,
            });

            index++;
        }
        isLoading = false;
    });

    const generatePO = (id: number) => {
        const doc = new jsPDF({
            orientation: "portrait",
            unit: "pt",
            format: "a4",
        });

        // const contentWidth = 10.5 * 28.8465; // ≈ 297.64 pt
        const contentWidth = 10.3 * 28.8465; // ≈ 297.64 pt

        // const contentHeight = 14.5 * 28.8465;
        const contentHeight = 14.2 * 28.8465;

        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();

        // Horizontally centered, top-aligned
        const originX = (pageWidth - contentWidth) / 2; // Centered horizontally
        const originY = 2; // Small top margin

        let x = originX + 5;
        let y = originY + 5;
        const lineHeight = 16;

        // Outer border
        doc.setDrawColor(0);
        doc.setLineWidth(1);
        doc.rect(originX, originY, contentWidth, contentHeight);

        // Created By
        doc.setFontSize(8);
        doc.text(
            "Created By: " + formData.createdByName,
            originX + contentWidth - 5,
            originY + 10,
            {
                align: "right",
            }
        );

        // Logo
        doc.addImage(LOGO_URL, "PNG", originX + 0, 0, 90, 70);
        doc.setFont("helvetica", "bold");
        doc.setFontSize(13);
        doc.text("Demand Slip", contentWidth + 50, originY + 30);
        doc.setFontSize(10);
        doc.setFont("helvetica", "normal");

        y += 70;

        // Sr. No. and Date
        doc.setFont("helvetica", "bold");
        doc.text("Order No:", x, y);
        doc.setTextColor(255, 0, 0);
        doc.text("USI-PO-" + formData.id, x + 48, y);
        doc.setTextColor(0, 0, 0);
        doc.text("Ref No:", x + 120, y);
        doc.setFont("helvetica", "normal");
        doc.text(formData.poNumber, x + 157, y);
        doc.setFont("helvetica", "bold");
        doc.text("Date:", x + 215, y);
        doc.setFont("helvetica", "normal");
        doc.text(formatDateUI(new Date(), false), x + 240, y);
        doc.line(originX, y + 7, originX + contentWidth, y + 7);

        // From/To Dept
        y += 25;

        doc.setFont("helvetica", "bold");
        doc.text("From Deptt:", x, y);
        doc.setFont("helvetica", "normal");
        const fromDepartmentText = formData.fromDepartment?.name?.toUpperCase() || "";
        if (fromDepartmentText.length <= 15) {
            doc.setFontSize(8);
        } else {
            doc.setFontSize(6);
        }
        doc.text(fromDepartmentText, x + 65, y);
        doc.setFontSize(10);
        doc.setFont("helvetica", "bold");
        doc.text("To Deptt:", x + 144, y);
        doc.setFont("helvetica", "normal");
        const toDepartmentText = formData.toDepartment?.name?.toUpperCase() || "";
        if (toDepartmentText.length <= 15) {
            doc.setFontSize(8);
        } else {
            doc.setFontSize(6);
        }
        doc.text(toDepartmentText, x + 190, y);
        doc.setFontSize(10);
        doc.line(originX, y + 7, originX + contentWidth, y + 7);

        // Name of Person
        y += 25;
        doc.setFont("helvetica", "bold");
        doc.text("Name of Person:", x, y);
        doc.setFont("helvetica", "normal");
        doc.setFontSize(10);
        const contactPerson = formData.supplierContactPerson || "";
        // const contactPerson = "Thsjmk SDfghju CGjhddopj HUJioddfgryiopju";
        if (contactPerson.length > 38) {
            doc.setFontSize(8);
        } else {
            doc.setFontSize(10);
        }
        doc.text(contactPerson, x + 83, y);
        doc.line(originX, y + 7, originX + contentWidth, y + 7);
        y += 25;
        doc.setFont("helvetica", "bold");
        doc.text("Supplier Name:", x, y);
        doc.setFont("helvetica", "normal");
        const supplierNameText = formData.supplier.name?.toUpperCase() || "";
        if (supplierNameText.length <= 38) {
            doc.setFontSize(10);
        } else {
            doc.setFontSize(8);
        }
        doc.text(capitalizeFirstWord(supplierNameText), x + 80, y);
        doc.line(originX, y + 7, originX + contentWidth, y + 7);

        // Table headers
        y += 7;
        const itemCodeWidth = 60;
        const unitWidth = 40;
        const qtyWidth = 60;
        const rateWidth = 70; // Fixed width for rate column
        const descriptionWidth = contentWidth - (itemCodeWidth + unitWidth + qtyWidth + rateWidth);
        const colWidths = [itemCodeWidth, descriptionWidth, unitWidth, qtyWidth, rateWidth];
        const headers = ["Item Code", "Description", "Unit", "Qty.", "Rate"];
        let colX = x;

        doc.setFont("helvetica", "bold");
        headers.forEach((text, i) => {
            // doc.rect(colX - 5, y, colWidths[i], lineHeight + 10);
            const x = colX - 5;
            const yTop = y;
            const width = colWidths[i];
            const height = lineHeight + 10;

            // Top border
            doc.line(x, yTop, x + width, yTop);

            // Left border
            doc.line(x, yTop, x, yTop + height);

            // Right border
            doc.line(x + width, yTop, x + width, yTop + height);
            doc.text(text, colX, y + 15);
            colX += colWidths[i];
        });

        // Table rows
        const MAX_LINES = 2;
        const MAX_ROWS = 5;
        const fixedRowHeight = lineHeight * MAX_LINES;

        let renderedRows = 0;

        const drawTableRow = (index: number, values: any[]) => {
            if (renderedRows >= MAX_ROWS) return;

            colX = x - 5;
            y += index != 1 ? fixedRowHeight : fixedRowHeight - 5;
            doc.setFont("helvetica", "normal");

            values.forEach((text: any, i: number) => {
                const str = text.toString();
                let lines = doc.splitTextToSize(str, colWidths[i] - 4);

                // Limit to 2 lines
                if (lines.length > MAX_LINES) {
                    lines = lines.slice(0, MAX_LINES);
                    lines[MAX_LINES - 1] += "...";
                }

                // Calculate vertical centering offset
                const totalTextHeight = lines.length * lineHeight;
                const offsetY = (fixedRowHeight - totalTextHeight) / 2;

                // Draw cell border
                doc.rect(colX, y, colWidths[i], fixedRowHeight);

                // Draw lines inside the cell
                lines.forEach((line: any, j: any) => {
                    doc.text(line, colX + 4, y + offsetY + 12 + j * lineHeight);
                });

                colX += colWidths[i];
            });

            renderedRows++;
        };

        const totalRows = 5;

        formData.items.forEach((item: any, index: number) => {
            drawTableRow(index + 1, [
                item.item.sku?.length <= 6 ? item.item.sku : "",
                item.item.name,
                item.item.unit.name,
                item.qty,
                item.item.priceData[0].price,
            ]);
        });

        // Fill remaining rows with blanks
        const rowsToPad = totalRows - formData.items.length;

        for (let i = 0; i < rowsToPad; i++) {
            drawTableRow(2, ["", "", "", "", ""]);
        }
        //totals - positioned to align with columns
        y += fixedRowHeight + 10;
        // doc.setFont("helvetica", "bold");
        // doc.text("Total:", x + colWidths[0], y);
        doc.setFont("helvetica", "normal");

        const itemCodeColumnX = x - 5; // Item Code column position
        const descriptionColumnX = x - 5 + colWidths[0]; // Description column position
        const unitColumnX = x - 5 + colWidths[0] + colWidths[1]; 
        const qtyColumnX = x - 5 + colWidths[0] + colWidths[1] + colWidths[2]; // Item Code + Description + Unit columns
        const rateColumnX = x - 5 + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3]; // Item Code + Description + Unit + Qty columns

        // Add borders for all total cells (without top border)
        const totalCellHeight = 20;
        const totalCellY = y;

        // Borders for item code column cell (left, bottom, right - no top) - empty cell
        doc.line(itemCodeColumnX, totalCellY, itemCodeColumnX, totalCellY + totalCellHeight); // Left border
        doc.line(
            itemCodeColumnX,
            totalCellY + totalCellHeight,
            itemCodeColumnX + colWidths[0],
            totalCellY + totalCellHeight
        ); // Bottom border
        doc.line(
            itemCodeColumnX + colWidths[0],
            totalCellY,
            itemCodeColumnX + colWidths[0],
            totalCellY + totalCellHeight
        ); // Right border

        // Borders for description column cell (left, bottom, right - no top) - empty cell
        doc.line(descriptionColumnX, totalCellY, descriptionColumnX, totalCellY + totalCellHeight); // Left border
        doc.line(
            descriptionColumnX,
            totalCellY + totalCellHeight,
            descriptionColumnX + colWidths[1],
            totalCellY + totalCellHeight
        ); // Bottom border
        doc.line(
            descriptionColumnX + colWidths[1],
            totalCellY,
            descriptionColumnX + colWidths[1],
            totalCellY + totalCellHeight
        ); // Right border

        doc.line(unitColumnX, totalCellY, unitColumnX, totalCellY + totalCellHeight); // Left border
        doc.line(
            unitColumnX,
            totalCellY + totalCellHeight,
            unitColumnX + colWidths[2],
            totalCellY + totalCellHeight
        ); // Bottom border
        doc.line(
            unitColumnX + colWidths[2],
            totalCellY,
            unitColumnX + colWidths[2],
            totalCellY + totalCellHeight
        ); // Right border

        // Borders for qty total cell (left, bottom, right - no top)
        doc.line(qtyColumnX, totalCellY, qtyColumnX, totalCellY + totalCellHeight); // Left border
        doc.line(
            qtyColumnX,
            totalCellY + totalCellHeight,
            qtyColumnX + colWidths[3],
            totalCellY + totalCellHeight
        ); // Bottom border
        doc.line(
            qtyColumnX + colWidths[3],
            totalCellY,
            qtyColumnX + colWidths[3],
            totalCellY + totalCellHeight
        ); // Right border

        // Borders for rate total cell (left, bottom - no top, no right for last column)
        doc.line(rateColumnX, totalCellY, rateColumnX, totalCellY + totalCellHeight); // Left border
        doc.line(
            rateColumnX,
            totalCellY + totalCellHeight,
            rateColumnX + colWidths[4],
            totalCellY + totalCellHeight
        ); // Bottom border
        // No right border for the last column

        // Set font to bold for all total text
        doc.setFont("helvetica", "bold");

        // Calculate center Y position for text within the cell
        const centerY = totalCellY + totalCellHeight / 2 + 4; // +4 for text baseline adjustment

        doc.text("Total:", unitColumnX + 4, centerY);

        doc.text(
            formData.items.reduce((acc, item) => acc + item.qty, 0).toString(),
            qtyColumnX + 4, // Add small padding like in table cells
            centerY
        );

        doc.text(
            Math.round(
                formData.items.reduce(
                    (acc, item) => acc + item.qty * item.item.priceData[0].price,
                    0
                )
            ).toString() + ".00",
            rateColumnX + 4, // Add small padding like in table cells
            centerY
        );
        // Reset font to normal
        doc.setFont("helvetica", "normal");

        // Footer

        doc.setFont("helvetica", "bold");
        doc.text("Required by Date:", x, contentHeight - 1);
        doc.text(formatDateUI(formData.expectedDate, false), x + 90, contentHeight - 1);
        doc.text("Auth Sig.:", x + 180, contentHeight - 1);
        doc.save(`Purchase-Order-${id}.pdf`);
    };
</script>

{#if isLoading}
    <PageLoader />
{:else}
    <div class="relative flex items-center justify-center mb-4 p-6">
        <div
            class="absolute inset-0 bg-cover bg-center rounded-lg"
            style="background: {theme.tableHeader}"
        ></div>

        <div class="w-[90vw] py-4 bg-background/70 backdrop-blur rounded-lg">
            <div class="flex items-center justify-between py-3 mb-4 rounded-lg p-4">
                <FormHeader label={"Demand Slip Item"}></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>

            <!-- Department and Contact Person Fields -->
            <div class="flex flex-col gap-4">
                <div class="grid grid-cols-3 gap-4 mt-5 rounded-lg p-4 pb-0">
                    <div>
                        <Label
                            for="purchaseOrderNo"
                            class="mb-2 font-sans capitalize tracking-[0px]"
                        >
                            Demand Slip No.
                        </Label>
                        <Input
                            type="text"
                            id="poNumber"
                            placeholder="PO No."
                            class="uppercase dark:bg-primary-700"
                            bind:value={formData.poNumber}
                            on:input={() => {
                                validationErrors = new Map();
                            }}
                        />
                        {#if validationErrors.has("poNumber")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("poNumber")}
                            </p>
                        {/if}
                    </div>
                    <div>
                        <SupplierSearch
                            label="Supplier"
                            selected={selectedSupplier}
                            onSelected={(data) => {
                                if (data) {
                                    formData = { ...formData, id: data.id };
                                    formData.supplier.id = data.id;
                                    formData.supplier.name = data.name;
                                    formData.items = [
                                        {
                                            item: {
                                                id: -1,
                                                name: "",
                                                gstPercentage: 0,
                                                hsn: "",
                                                msq: 0,
                                                sku: "",
                                                priceData: [],
                                                unit: {
                                                    id: -1,
                                                    name: "",
                                                } as IItemUnit,
                                                category: {
                                                    id: -1,
                                                    name: "",
                                                } as IItemCategory,
                                            },
                                            qty: 0,
                                        },
                                    ];
                                } else {
                                    formData.supplier.id = -1;
                                    formData.items = [];
                                }
                                selectedSupplier = data;
                                validationErrors = new Map();
                            }}
                        />
                        {#if validationErrors.has("supplier")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("supplier")}
                            </p>
                        {/if}
                    </div>
                    <div>
                        <Label for="expectedData" class="mb-2 font-sans capitalize tracking-[0px]">
                            Expected Date
                        </Label>
                        <Input
                            type="date"
                            id="expectedData"
                            placeholder="Expected Data"
                            class="dark:bg-primary-700"
                            value={formatDateToInput(formData.expectedDate)}
                            onchange={(e: any) => {
                                formData.expectedDate = new Date(e.target.value);
                            }}
                        />
                        {#if validationErrors.has("expectedDate")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("expectedDate")}
                            </p>
                        {/if}
                    </div>
                </div>
                <div
                    class="my-2 w-full h-2 bg-cover bg-center"
                    style="background: {theme.tableHeader}"
                ></div>
                <!-- Department and Contact Person Fields -->
                <div class=" grid grid-cols-3 gap-4 mt-5 p-4">
                    <div>
                        <DepartmentDropdown
                            data={departmentData}
                            labelText="From Department"
                            selectedValue={selectedFromDepartment?.id || -1}
                            onSelected={(data: IDepartment | null) => {
                                selectedFromDepartment = data;
                                formData.fromDepartment = data;
                                validationErrors = new Map();
                            }}
                            excludeId={selectedToDepartment?.id}
                            disabled={isDepartmentLoading}
                            errorMap={validationErrors}
                            fieldName="fromDepartment"
                            placeholder="Select From Department"
                        />
                    </div>
                    <div>
                        <DepartmentDropdown
                            data={departmentData}
                            labelText="To Department"
                            selectedValue={selectedToDepartment?.id || -1}
                            onSelected={(data: IDepartment | null) => {
                                selectedToDepartment = data;
                                formData.toDepartment = data;
                                validationErrors = new Map();
                            }}
                            excludeId={selectedFromDepartment?.id}
                            disabled={isDepartmentLoading}
                            errorMap={validationErrors}
                            fieldName="toDepartment"
                            placeholder="Select To Department"
                        />
                    </div>
                    <div>
                        <Label
                            for="supplierContactPerson"
                            class="mb-2 font-sans capitalize tracking-[0px]"
                        >
                            Supplier Contact Person
                        </Label>
                        <Input
                            type="text"
                            id="supplierContactPerson"
                            placeholder="Contact Person"
                            class="dark:bg-primary-700"
                            bind:value={formData.supplierContactPerson}
                            on:input={() => {
                                validationErrors = new Map();
                            }}
                        />
                        {#if validationErrors.has("supplierContactPerson")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("supplierContactPerson")}
                            </p>
                        {/if}
                    </div>
                </div>
            </div>

            <div class="m-2"></div>

            <div
                class="my-2 w-full h-2 bg-cover bg-center"
                style="background: {theme.tableHeader}"
            ></div>

            <div class="overflow-x-auto bg-background/40 p-4 m-4 rounded-lg">
                <span class="text-sm italic">Items</span>
                <table
                    class="w-full text-left text-sm text-secondary-dark dark:text-gray-400 rtl:text-right border"
                >
                    <thead class="text-accent bg-background/70">
                        <tr class="">
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                SR No.
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Item
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Total Qty
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Action
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-background/50">
                        {#each formData.items as item, index}
                            <tr
                                class="border-b bg-snow text-secondary-dark hover:bg-secondary-soft dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    {index + 1}
                                </td>
                                <td class="px-6 py-4 w-[300px] h-[60px]">
                                    <div class="mb-[35px] w-[250px]">
                                        <SupplierRawMaterialSearch
                                            supplierId={formData.supplier.id}
                                            selected={getSelected(index)}
                                            onSelected={(data) => {
                                                onRawMaterialSelected(data, item, index);
                                            }}
                                        />
                                        {#if validationErrors.has("expectedDate")}
                                            <p
                                                class="pt-2 font-serif text-[14px] italic text-red-500"
                                            >
                                                {validationErrors.get("expectedDate")}
                                            </p>
                                        {/if}
                                    </div>
                                </td>

                                <td class="px-6 py-4">
                                    <Input
                                        class="mb-[35px] w-[100px]"
                                        type="number"
                                        value={item.qty}
                                        on:input={(e) => {
                                            handleTotalQty(e, index);
                                        }}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    <CloseButton
                                        class="w-content mb-[35px]"
                                        on:click={() => {
                                            if (formData.items.length === 1) {
                                                showErrorToast("At least one item is required");
                                                return;
                                            }
                                            const deletedItemId = formData.items[index].item.id;

                                            // Remove the item from the array (using slice for reactivity)
                                            formData.items = formData.items.filter(
                                                (item) => item.item.id !== deletedItemId
                                            );

                                            // Update selectedItemsMap based on IDs instead of indexes
                                            selectedItemsMap.delete(String(deletedItemId));

                                            // Update oldRawMaterialsIds correctly
                                            oldRawMaterialsIds = oldRawMaterialsIds.filter(
                                                (id) => id !== deletedItemId
                                            );
                                        }}
                                    />
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>

            <div class="mt-5 flex w-full justify-between">
                <div>
                    {#if formData.items.length > 0}
                        <CustomButton
                            onClick={() => {
                                formData.items.push({
                                    item: {
                                        id: -1,
                                        name: "",
                                        gstPercentage: 0,
                                        hsn: "",
                                        msq: 0,
                                        sku: "",
                                        unit: {
                                            id: -1,
                                            name: "",
                                        } as IItemUnit,
                                        category: {
                                            id: -1,
                                            name: "",
                                        } as IItemCategory,
                                        priceData: [],
                                    },
                                    qty: 0,
                                });
                                formData = formData;
                            }}
                            cssClass=" bg-primary text-snow"
                            title={"Add new item"}
                        />
                    {/if}
                </div>
                <div class="flex">
                    <CustomButton
                        onClick={onSubmitHandler}
                        cssClass="w-32 bg-accent text-snow mr-2"
                        title={"Save"}
                    />
                    {#if isEditCase}
                        <CustomButton
                            onClick={() => {
                                generatePO(formData.id);
                            }}
                            cssClass="w-32 bg-primary text-snow"
                            title={"Print"}
                        />
                    {/if}
                </div>
            </div>
        </div>
    </div>
{/if}

<!-- {#if isEditCase}
    <div
        id="print-stock-issuance"
        style="width:350px; margin:auto; font-family:sans-serif; border:1px solid #000;"
    >
       
        <div>
            <div
                style="font-size: 8px; font-weight: bold; text-align: right; text-transform: uppercase; padding: 10px 10px 0 0;"
            >
                Created By: {$loggedInUser?.firstName}
                {$loggedInUser?.lastName}
            </div>
        </div>

        <div>
            <div style="text-align: center; padding: 5px;">
                <img
                    src="/images/logo.png"
                    alt="logo"
                    style="height: 30px; display: block; margin: 0 auto;"
                />
            </div>
        </div>
        <table style="width:100%; border-collapse:collapse; font-size:13px;">
            <tbody>
                <tr>
                    <td style="width:60%; border-bottom:1px solid #000; padding:2px;">
                        Sr. No:
                        <span style="color:red; font-weight:bold; padding-left: 5px;">
                            USI-PO-{formData.id}
                        </span>
                    </td>
                    <td style="border-bottom:1px solid #000; padding:2px;">
                        Date:
                        <span style="font-weight:bold; padding-left: 5px;">
                            {formatDateUI(new Date(), false)}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="border-bottom:1px solid #000; padding:2px;">From Deptt:</td>
                    <td style="border-bottom:1px solid #000; padding:2px;">To Deptt:</td>
                </tr>
                <tr>
                    <td colspan="2" style="border-bottom:1px solid #000; padding:2px;">
                        Name of Person:
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style=" padding:2px;">
                        Name of Party:
                        <span style="font-weight:bold; padding-left: 5px;">
                            {formData.supplier.name}
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
        <table style="width:100%; border-collapse:collapse; font-size:13px;">
            <thead>
                <tr>
                    <th style="border:1px solid #000; padding:2px; width:18%;">Item Code</th>
                    <th style="border:1px solid #000; padding:2px;">Description</th>
                    <th style="border:1px solid #000; padding:2px; width:15%;">Qty.</th>
                    <th style="border:1px solid #000; padding:2px; width:15%;">Rate</th>
                </tr>
            </thead>
            <tbody style="min-height: 200px; margin:auto;">
                {#each formData.items as item, index}
                    <tr>
                        <td style="border:1px solid #000; margin:auto; text-align:center;">
                            {index + 1}
                        </td>
                        <td style="border:1px solid #000; margin:auto; text-align:center;">
                            {item.item.name}
                        </td>
                        <td style="border:1px solid #000; margin:auto; text-align:center;">
                            {item.qty}
                        </td>
                        <td style="border:1px solid #000; margin:auto; text-align:center;">
                            {item.item.price}
                        </td>
                    </tr>
                {/each}
                {#each Array(Math.max(0, 4 - formData.items.length)) as _, i}
                    <tr>
                        <td style="border:1px solid #000; padding:8px 8px;"></td>
                        <td style="border:1px solid #000;"></td>
                        <td style="border:1px solid #000;"></td>
                        <td style="border:1px solid #000;"></td>
                    </tr>
                {/each}
            </tbody>
        </table>
        <table style="width:100%; border-collapse:collapse; font-size:13px; margin-top:8px;">
            <tbody>
                <tr>
                    <td style="width:60%; padding:2px;">Required by Date:</td>
                    <td style="padding:2px;">Auth Sig.:</td>
                </tr>
            </tbody>
        </table>
    </div>

    <style>
        #print-stock-issuance {
            /* visibility: hidden; */
        }
    </style>
{/if} -->
