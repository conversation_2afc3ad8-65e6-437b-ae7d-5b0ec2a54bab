<script lang="ts">
    import { goto } from "$app/navigation";
    import Loader from "$lib/common/components/Loader.svelte";
    import { Spinner } from "flowbite-svelte";
    import type { IPurchaseOrder } from "../models/IPurchaseOrder";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { onMount } from "svelte";
    import { debounce, formatDateUI, showErrorToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import headerPattern from "$lib/common/assets/pattern/table-header.png";
    import theme from "$lib/common/assets/theme";

    export let isLoading: boolean = false;
    export let searchTerm: string = "";
    export let filteredData: IPurchaseOrder[] = [];
    export let paginationData: PaginatedDataWrapper<IPurchaseOrder>;
    export let onSearchClear: () => void = () => {};
    let isSearching: boolean = false;
    let alreadyFetchedData: IPurchaseOrder[] = [];


    // Page size input functionality
    let pageSizeInput: number = paginationData.pageSize;

    // Handle page size change
    function handlePageSizeChange() {
        if (pageSizeInput && pageSizeInput > 0) {
            paginationData.pageSize = pageSizeInput;
            paginationData.onPageChange(1); // Reset to first page when changing page size
        }
    }

    let debounceSearch = debounce(async (e: any) => {
        if (searchTerm.trim().length === 0) {
            paginationData.searchText = undefined;
            onSearchClear();
            return;
        }
        if (e.target.value.trim().length > 2) {
            isSearching = true;

            paginationData.searchText = e.target.value.trim();

            const result = await PresenterProvider.purchaseOrderPresenter.getAll(
                paginationData.pagination.currentPage,
                paginationData.pageSize,
                paginationData.searchText!,
            );
            if (!result.success) {
                return showErrorToast(result.message);
            } else {
                filteredData = result.data.data;

                paginationData.pagination = result.data;

                isSearching = false;
            }
        } else {
            filteredData = alreadyFetchedData;
        }
    }, 600);

    onMount(() => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;

        searchTerm = paginationData.searchText || "";
    });
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <div class="mb-6">
        <h1 class="mb-1 text-xl font-bold text-accent">Demand Slip</h1>
        <p class="text-accent/50 text-sm">Manage your purchase orders and demand slips</p>
    </div>

    <div class="overflow-hidden rounded-xl border border-foreground/20 bg-background shadow-lg">
        <!-- Search and Actions Bar -->
        <div
            class="flex justify-between rounded-t-xl border-b border-foreground/20 bg-cover bg-center bg-repeat p-6"
            style={`background: ${theme.tableHeader};`}
        >
            <div class="relative w-full md:w-80">
                <div
                    class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 z-10"
                >
                    <svg
                        class="h-4 w-4 text-black/70"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        ></path>
                    </svg>
                </div>
                <input
                    type="text"
                    id="purchase-order-search"
                    bind:value={searchTerm}
                    on:input={debounceSearch}
                    class="w-full rounded-lg border border-accent/20 bg-background backdrop-blur-sm px-4 py-2.5 pl-10 text-sm text-black placeholder:text-black/50 focus:border-accent focus:outline-none focus:ring-2 focus:ring-accent/10 transition-all duration-300 relative z-0"
                    placeholder="Search by PO Number"
                />
                {#if isSearching}
                    <div class="absolute right-3 top-1/2 -translate-y-1/2 z-10">
                        <Loader />
                    </div>
                {/if}
            </div>

            <button
                id="dropdownActionButton"
                data-dropdown-toggle="dropdownAction"
                class="inline-flex items-center rounded-lg bg-background px-4 py-2.5 text-sm font-semibold hover:bg-background/90 text-accent shadow-md transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-accent/20"
                type="button"
                on:click={() => {
                    goto("/admin/purchase-orders/add");
                }}
            >
                <svg
                    class="mr-2 h-4 w-4 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    ></path>
                </svg>
                Add New Order
            </button>
        </div>

        {#if isSearching}
            <div class="flex h-80 w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <div class="overflow-x-auto">
                <table class="w-full text-left text-sm">
                    <thead class="sticky top-0 bg-foreground/50 text-accent shadow-md">
                        <tr>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                SR No.
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Supplier
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                PO Number
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                PO Ref Number
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                From Dept
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                To Dept
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Raw Material
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Expected Date
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-accent/10">
                        {#each filteredData as row, index}
                            <tr
                                class="bg-background text-black transition-all duration-300 hover:bg-foreground/20 hover:shadow-sm ease-in-out"
                            >
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-semibold text-accent hover:text-accent/80 transition-all duration-300 ease-in-out"
                                    >
                                        <span
                                            class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-accent/10 text-accent text-xs font-bold"
                                        >
                                            {(paginationData.pagination.currentPage - 1) *
                                                paginationData.pageSize +
                                                index +
                                                1}
                                        </span>
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {row.supplier.name}
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {row.id}
                                    </a>
                                </td>
                                
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {row.poNumber}
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {row.fromDepartment?.name || "N/A"}
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {row.toDepartment?.name || "N/A"}
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {row.items.map((item) => item.item.name).join(", ")}
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {formatDateUI(row.expectedDate, false)}
                                    </a>
                                </td>
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr>
                                <td colspan="7" class="h-80 text-center text-black/60">
                                    <div
                                        class="flex flex-col items-center justify-center space-y-3"
                                    >
                                        <div class="relative">
                                            <div
                                                class="absolute inset-0 bg-gradient-to-r from-accent/20 to-accent/10 rounded-full blur-lg"
                                            ></div>
                                            <svg
                                                class="relative h-12 w-12 text-accent/60"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="1.5"
                                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                ></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-base font-semibold">
                                                No demand slips found
                                            </p>
                                            <p class="text-sm text-black/40">
                                                {searchTerm.trim().length > 0
                                                    ? "Try adjusting your search criteria"
                                                    : "Get started by creating your first demand slip"}
                                            </p>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>
{/if}




    <!-- Pagination Section -->
    {#if paginationData.pagination.totalData > 0}
        <div
            class="mt-6 flex items-center justify-between rounded-xl bg-gradient-to-r from-background to-background/95 p-5 shadow-lg border border-accent/10"
        >
            <!-- Page Info -->
            <div class="text-sm font-semibold text-black/80 bg-accent/5 px-3 py-1.5 rounded-md">
                Page {paginationData.pagination.currentPage} of {paginationData.pagination
                    .totalPages}
            </div>

            <!-- Pagination Controls -->
            <div class="flex items-center gap-2">
                <!-- Previous Button -->
                <button
                    on:click={() => {
                        if (paginationData.pagination.currentPage > 1) {
                            paginationData.onPageChange(paginationData.pagination.currentPage - 1);
                        }
                    }}
                    disabled={paginationData.pagination.currentPage === 1 || isLoading}
                    class="flex items-center justify-center rounded-lg border border-accent/20 bg-background px-3 py-2 text-sm font-semibold text-black transition-all duration-300 hover:bg-accent/5 hover:border-accent/40 hover:shadow-md disabled:disabled:cursor-not-allowed disabled:hover:shadow-none"
                    title="Previous page"
                >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 19l-7-7 7-7"
                        ></path>
                    </svg>
                </button>

                <!-- Page Numbers -->
                <div class="flex items-center gap-1">
                    {#each Array.from({ length: paginationData.pagination.totalPages }, (_, i) => i + 1) as pageNum}
                        {#if pageNum === 1 || pageNum === paginationData.pagination.totalPages || (pageNum >= paginationData.pagination.currentPage - 1 && pageNum <= paginationData.pagination.currentPage + 1)}
                            <button
                                on:click={() => paginationData.onPageChange(pageNum)}
                                disabled={isLoading}
                                class="flex h-8 w-8 items-center justify-center rounded-lg text-sm font-bold transition-all duration-300 {pageNum ===
                                paginationData.pagination.currentPage
                                    ? 'bg-gradient-to-r from-accent to-accent/90 text-background shadow-md scale-105'
                                    : 'bg-background text-black border border-accent/20 hover:bg-accent/5 hover:border-accent/40 hover:shadow-sm hover:scale-102'} disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {pageNum}
                            </button>
                        {:else if pageNum === paginationData.pagination.currentPage - 2 || pageNum === paginationData.pagination.currentPage + 2}
                            <span
                                class="flex h-8 w-8 items-center justify-center text-sm text-black/40 font-bold"
                            >
                                ...
                            </span>
                        {/if}
                    {/each}
                </div>

                <!-- Next Button -->
                <button
                    on:click={() => {
                        if (
                            paginationData.pagination.currentPage <
                            paginationData.pagination.totalPages
                        ) {
                            paginationData.onPageChange(paginationData.pagination.currentPage + 1);
                        }
                    }}
                    disabled={paginationData.pagination.currentPage ===
                        paginationData.pagination.totalPages || isLoading}
                    class="flex items-center justify-center rounded-lg border border-accent/20 bg-background px-3 py-2 text-sm font-semibold text-black transition-all duration-300 hover:bg-accent/5 hover:border-accent/40 hover:shadow-md disabled:disabled:cursor-not-allowed disabled:hover:shadow-none"
                    title="Next page"
                >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 5l7 7-7 7"
                        ></path>
                    </svg>
                </button>
            </div>

            <!-- Page Size Input -->
            <div class="flex items-center gap-3">
                <div class="relative">
                    <input
                        type="number"
                        min="1"
                        max="1000"
                        bind:value={pageSizeInput}
                        on:keydown={(e) => {
                            if (e.key === "Enter") {
                                handlePageSizeChange();
                            }
                        }}
                        class="w-20 rounded-lg border border-accent/20 bg-background px-3 py-2 text-sm text-black focus:border-accent focus:outline-none focus:ring-2 focus:ring-accent/10 transition-all duration-300"
                        disabled={isLoading}
                        placeholder="Items"
                    />
                </div>
                <button
                    on:click={handlePageSizeChange}
                    disabled={isLoading}
                    class="rounded-lg bg-gradient-to-r from-accent to-accent/90 px-4 py-2 text-sm font-semibold text-background shadow-md transition-all duration-300 hover:shadow-lg hover:scale-102 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Apply
                </button>
            </div>
        </div>
    {/if}
