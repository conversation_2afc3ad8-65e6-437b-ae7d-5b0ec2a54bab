<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    import {
        capitalizeFirstWord,
        debounce,
        getDateDifference,
        getFutureDate,
        getPastDate,
        showErrorToast,
    } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import { goto } from "$app/navigation";
    import Loader from "$lib/common/components/Loader.svelte";
    import type { IRawMaterialOverview, IRawMaterialVariation } from "../models/IRawMaterial";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { downloadAllCsv, downloadCsv } from "$lib/raw_material/utils/RawMaterialUtils";
    import headerPattern from "$lib/common/assets/pattern/table-header.png";
    import theme from "$lib/common/assets/theme";

    const exportAllDate: {
        startDate: Date;
        endDate: Date;
        loading: boolean;
        exportAllLoading: boolean;
    } = {
        startDate: getPastDate(new Date(), 365),
        endDate: new Date(),
        loading: false,
        exportAllLoading: false,
    };

    export let searchTerm: string = "";
    export let paginationData: PaginatedDataWrapper<IRawMaterialOverview>;
    export let onSearchClear: () => void = () => {};

    let isLoading: boolean = false;
    let isSearching: boolean = false;
    let filteredData: IRawMaterialOverview[] = [];
    let alreadyFetchedData: IRawMaterialOverview[] = [];

    // Page size input functionality
    let pageSizeInput: number = paginationData.pageSize;

    // Handle page size change
    function handlePageSizeChange() {
        if (pageSizeInput && pageSizeInput > 0) {
            paginationData.pageSize = pageSizeInput;
            paginationData.onPageChange(1); // Reset to first page when changing page size
        }
    }

    let debounceSearch = debounce(async (e: any) => {
        if (searchTerm.trim().length === 0) {
            paginationData.searchText = undefined;
            onSearchClear();
            return;
        }
        if (e.target.value.trim().length > 2) {
            searchTerm = e.target.value.trim();
            paginationData.searchText = searchTerm;

            isSearching = true;
            const result = await PresenterProvider.rawMaterialPresenter.getRawMaterials(
                1,
                paginationData.pageSize,
                e.target.value.trim()
            );
            if (!result.success) {
                return showErrorToast(result.message);
            } else {
                filteredData = result.data.data;
                isSearching = false;
            }
        } else {
            filteredData = alreadyFetchedData;
        }
    }, 850);

    onMount(async () => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;
        pageSizeInput = paginationData.pageSize;
    });

    // Update page size input when page size changes
    $: {
        pageSizeInput = paginationData.pageSize;
    }

    // Selection state
    export let selectedRowsMap: Map<number, IRawMaterialOverview> = new Map();
    // let selectedRows :IRawMaterialOverview[]=[];
    let selectAll = false;

    // Toggle row selection
    function toggleRowSelection(row: IRawMaterialOverview, id: number) {
        if (selectedRowsMap.has(id)) {
            selectedRowsMap.delete(id);
        } else {
            selectedRowsMap.set(id, row);
        }
    }

    // Toggle all selections
    function toggleSelectAll() {
        if (selectAll) {
            filteredData.forEach((rmData) => {
                selectedRowsMap.set(rmData.id, rmData);
            });
        } else {
            selectedRowsMap.clear();
            selectAll = false;
        }
        selectedRowsMap = selectedRowsMap;
    }
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-full items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <div class="mb-6">
        <h1 class="mb-1 text-xl font-bold text-accent">Raw Materials</h1>
        <p class="text-accent/50 text-sm">Manage raw materials and their configurations</p>
    </div>

    <div class="flex my-3 px-1 flex-col md:flex-row items-center gap-4 justify-end">
        <!-- Export Button -->
        <div class="h-full flex items-center">
            <button
                id="dropdownActionButton"
                data-dropdown-toggle="dropdownAction"
                class="inline-flex items-center justify-center rounded-lg border border-accent/20 text-accent px-4 py-2 text-sm font-medium hover:bg-accent/5 focus:outline-none focus:ring-4 focus:ring-accent/10 transition-all duration-300"
                type="button"
                disabled={exportAllDate.exportAllLoading}
                on:click={async () => {
                    exportAllDate.exportAllLoading = true;
                    await downloadAllCsv(exportAllDate.startDate, exportAllDate.endDate);
                    exportAllDate.exportAllLoading = false;
                }}
            >
                {#if exportAllDate.exportAllLoading}
                    <div class="flex gap-2 items-center justify-center">
                        Loading <Spinner class="size-5" />
                    </div>
                {:else}
                    Export All
                {/if}
            </button>
        </div>
    </div>

    <div class="overflow-hidden rounded-xl border border-foreground/20 bg-background shadow-lg">
        <!-- Search and Actions Bar -->
        <div
            class="flex justify-between rounded-t-xl border-b border-foreground/20 bg-cover bg-center bg-repeat p-6"
            style={`background: ${theme.tableHeader};`}
        >
            <div class="relative w-full md:w-80">
                <div
                    class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 z-10"
                >
                    <svg
                        class="h-4 w-4 text-black/70"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        ></path>
                    </svg>
                </div>
                <input
                    type="text"
                    id="raw-material-search"
                    bind:value={searchTerm}
                    on:input={debounceSearch}
                    class="w-full rounded-lg border border-accent/20 bg-background backdrop-blur-sm px-4 py-2.5 pl-10 text-sm text-black placeholder:text-black/50 focus:border-accent focus:outline-none focus:ring-2 focus:ring-accent/10 transition-all duration-300 relative z-0"
                    placeholder="Search by name..."
                />
                {#if isSearching}
                    <div class="absolute right-3 top-1/2 -translate-y-1/2 z-10">
                        <Loader />
                    </div>
                {/if}
            </div>

            <div class="flex space-x-4 items-center">
                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center rounded-lg bg-background px-4 py-2.5 text-sm font-semibold hover:bg-background/90 text-accent shadow-md transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-accent/20"
                    type="button"
                    on:click={() => {
                        goto("/admin/raw-materials/add");
                    }}
                >
                    <svg
                        class="mr-2 h-4 w-4 transition-transform duration-300"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        ></path>
                    </svg>
                    Add New
                </button>
                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center rounded-lg bg-background px-4 py-2.5 text-sm font-semibold hover:bg-background/90 text-accent shadow-md transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-accent/20"
                    type="button"
                    disabled={exportAllDate.loading}
                    on:click={async () => {
                        exportAllDate.loading = true;
                        await downloadCsv([...selectedRowsMap.values()], "Raw Material");
                        exportAllDate.loading = false;
                    }}
                >
                    {#if exportAllDate.loading}
                        <div class="flex gap-2 items-center justify-center">
                            Loading <Spinner class="size-5" />
                        </div>
                    {:else}
                        Export
                    {/if}
                </button>
            </div>
        </div>

        <!-- Table Section -->
        {#if isSearching}
            <div class="flex h-80 w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <div class="overflow-x-auto">
                <table class="w-full text-left text-sm">
                    <thead class="sticky top-0 bg-foreground/50 text-accent shadow-md">
                        <tr>
                            <th class="px-4 py-3">
                                <input
                                    type="checkbox"
                                    bind:checked={selectAll}
                                    on:change={toggleSelectAll}
                                />
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                SR No.
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Category
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Name
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                Unit
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                HSN
                            </th>
                            <th
                                scope="col"
                                class="px-6 py-3 font-bold uppercase tracking-wider text-sm"
                            >
                                GST %
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-accent/10">
                        {#each filteredData as row, index}
                            <tr
                                class="bg-background text-black transition-all duration-300 hover:bg-foreground/20 hover:shadow-sm ease-in-out"
                            >
                                <td class="px-4 py-4">
                                    <input
                                        type="checkbox"
                                        checked={selectedRowsMap.has(row.id)}
                                        on:change={() => toggleRowSelection(row, row.id)}
                                    />
                                </td>

                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/raw-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-semibold text-accent hover:text-accent/80 transition-all duration-300 ease-in-out"
                                    >
                                        <span
                                            class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-accent/10 text-accent text-xs font-bold"
                                        >
                                            {(paginationData.pagination.currentPage - 1) *
                                                paginationData.pageSize +
                                                index +
                                                1}
                                        </span>
                                    </a>
                                </td>

                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/raw-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        <span
                                            class="inline-flex items-center rounded-full bg-gradient-to-r from-accent/20 to-accent/10 px-3 py-1.5 text-sm font-semibold text-accent shadow-sm"
                                        >
                                            {capitalizeFirstWord(row.categoryName)}
                                        </span>
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/raw-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {capitalizeFirstWord(row.name)}
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/raw-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        <span
                                            class="inline-flex items-center px-2 py-0.5 rounded-full bg-accent/5 text-accent text-xs"
                                        >
                                            {row.unitName.toUpperCase()}
                                        </span>
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/raw-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {row.hsn}
                                    </a>
                                </td>
                                <td class="px-6 py-3">
                                    <a
                                        href={"/admin/raw-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium text-black hover:text-accent transition-all duration-300"
                                    >
                                        {row.gstPercentage}
                                    </a>
                                </td>
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr>
                                <td colspan="7" class="h-80 text-center text-black/60">
                                    <div
                                        class="flex flex-col items-center justify-center space-y-3"
                                    >
                                        <div class="relative">
                                            <div
                                                class="absolute inset-0 bg-gradient-to-r from-accent/20 to-accent/10 rounded-full blur-lg"
                                            ></div>
                                            <svg
                                                class="relative h-12 w-12 text-accent/60"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="1.5"
                                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                                                ></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-base font-semibold">
                                                No raw materials found
                                            </p>
                                            <p class="text-sm text-black/40">
                                                Try adjusting your search criteria or add a new raw
                                                material
                                            </p>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>

    <!-- Pagination Section -->
    {#if paginationData.pagination.totalData > 0}
        <div
            class="mt-6 flex items-center justify-between rounded-xl bg-gradient-to-r from-background to-background/95 p-5 shadow-lg border border-accent/10"
        >
            <!-- Page Info -->
            <div class="text-sm font-semibold text-black/80 bg-accent/5 px-3 py-1.5 rounded-md">
                Page {paginationData.pagination.currentPage} of {paginationData.pagination
                    .totalPages}
            </div>

            <!-- Pagination Controls -->
            <div class="flex items-center gap-2">
                <!-- Previous Button -->
                <button
                    on:click={() => {
                        if (paginationData.pagination.currentPage > 1) {
                            paginationData.onPageChange(paginationData.pagination.currentPage - 1);
                        }
                    }}
                    disabled={paginationData.pagination.currentPage === 1 || isLoading}
                    class="flex items-center justify-center rounded-lg border border-accent/20 bg-background px-3 py-2 text-sm font-semibold text-black transition-all duration-300 hover:bg-accent/5 hover:border-accent/40 hover:shadow-md disabled:disabled:cursor-not-allowed disabled:hover:shadow-none"
                    title="Previous page"
                >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 19l-7-7 7-7"
                        ></path>
                    </svg>
                </button>

                <!-- Page Numbers -->
                <div class="flex items-center gap-1">
                    {#each Array.from({ length: paginationData.pagination.totalPages }, (_, i) => i + 1) as pageNum}
                        {#if pageNum === 1 || pageNum === paginationData.pagination.totalPages || (pageNum >= paginationData.pagination.currentPage - 1 && pageNum <= paginationData.pagination.currentPage + 1)}
                            <button
                                on:click={() => paginationData.onPageChange(pageNum)}
                                disabled={isLoading}
                                class="flex h-8 w-8 items-center justify-center rounded-lg text-sm font-bold transition-all duration-300 {pageNum ===
                                paginationData.pagination.currentPage
                                    ? 'bg-gradient-to-r from-accent to-accent/90 text-background shadow-md scale-105'
                                    : 'bg-background text-black border border-accent/20 hover:bg-accent/5 hover:border-accent/40 hover:shadow-sm hover:scale-102'} disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {pageNum}
                            </button>
                        {:else if pageNum === paginationData.pagination.currentPage - 2 || pageNum === paginationData.pagination.currentPage + 2}
                            <span
                                class="flex h-8 w-8 items-center justify-center text-sm text-black/40 font-bold"
                            >
                                ...
                            </span>
                        {/if}
                    {/each}
                </div>

                <!-- Next Button -->
                <button
                    on:click={() => {
                        if (
                            paginationData.pagination.currentPage <
                            paginationData.pagination.totalPages
                        ) {
                            paginationData.onPageChange(paginationData.pagination.currentPage + 1);
                        }
                    }}
                    disabled={paginationData.pagination.currentPage ===
                        paginationData.pagination.totalPages || isLoading}
                    class="flex items-center justify-center rounded-lg border border-accent/20 bg-background px-3 py-2 text-sm font-semibold text-black transition-all duration-300 hover:bg-accent/5 hover:border-accent/40 hover:shadow-md disabled:disabled:cursor-not-allowed disabled:hover:shadow-none"
                    title="Next page"
                >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 5l7 7-7 7"
                        ></path>
                    </svg>
                </button>
            </div>

            <!-- Page Size Input -->
            <div class="flex items-center gap-3">
                <div class="relative">
                    <input
                        type="number"
                        min="1"
                        max="1000"
                        bind:value={pageSizeInput}
                        on:keydown={(e) => {
                            if (e.key === "Enter") {
                                handlePageSizeChange();
                            }
                        }}
                        class="w-20 rounded-lg border border-accent/20 bg-background px-3 py-2 text-sm text-black focus:border-accent focus:outline-none focus:ring-2 focus:ring-accent/10 transition-all duration-300"
                        disabled={isLoading}
                        placeholder="Items"
                    />
                </div>
                <button
                    on:click={handlePageSizeChange}
                    disabled={isLoading}
                    class="rounded-lg bg-gradient-to-r from-accent to-accent/90 px-4 py-2 text-sm font-semibold text-background shadow-md transition-all duration-300 hover:shadow-lg hover:scale-102 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Apply
                </button>
            </div>
        </div>
    {/if}
{/if}
