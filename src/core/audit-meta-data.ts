import { DataTypes, Includeable } from "sequelize";

/**
 * Base metadata fields for database tables
 * Includes primary key configuration
 * @constant
 * @type {Object}
 * @property {Object} id - Primary key configuration
 * @property {DataTypes} id.type - Integer data type
 * @property {boolean} id.autoIncrement - Auto-incrementing primary key
 * @property {boolean} id.primaryKey - Designates this as the primary key
 */
const PrefixMetaTableData = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
    },
}

/**
 * Audit metadata fields for tracking record changes
 * Includes creation, update, and deletion tracking
 * @constant
 * @type {Object}
 * @property {Object} c_by - ID of user who created the record
 * @property {Object} c_at - Timestamp of record creation
 * @property {Object} u_by - ID of user who last updated the record
 * @property {Object} u_at - Timestamp of last update
 * @property {Object} d_by - ID of user who deleted the record
 * @property {Object} d_at - Timestamp of deletion (for soft deletes)
 */
const SuffixMetaTableData = {
    c_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    u_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    d_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    c_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
    },
    u_at: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
    },
    d_at: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: null,
    },
}

export {
    PrefixMetaTableData,
    SuffixMetaTableData
}