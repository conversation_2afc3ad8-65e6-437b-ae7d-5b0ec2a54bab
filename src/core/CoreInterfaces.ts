interface AuditMetaData {
    createdById: number;
    createdAt: Date;
    updatedById: number | null;
    updatedAt: Date | null;
    deletedById: number | null;
    deletedAt: Date | null;
}


type AuditMetaKeys = "createdById" | "createdAt" | "updatedById" | "updatedAt" | "deletedById" | "deletedAt";


interface ActionByInfo {
    id: number;
    firstName:string;
    lastName:string;
    email:string
}

interface AuditData {
    auditData: {
        createdBy: ActionByInfo;
        updatedBy: ActionByInfo | null;
        deletedBy: ActionByInfo | null;
    }
}



interface InterfaceMetaData extends AuditMetaData {
    id: number;
}

interface APIBaseResponse<T> {
    success: boolean;
    message: string;
    data: T | null;
}

interface PaginatedBaseResponse<T> {
    totalData: number;
    currentPage: number;
    totalPages: number;
    data: T[];
}


type DTO<T> = APIBaseResponse<T>


interface BaseMetaUsers {
    createdBy: number;
    updatedBy: number | null;
    deletedBy: number | null;
}

interface MetaUser {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
}
interface BaseMeta extends BaseMetaUsers {
    id: number;
    createdAt: Date;
    updatedAt: Date | null;
    deletedAt: Date | null;
}
interface ParsedMeta {
    createdBy: MetaUser;
    updatedBy: MetaUser | null;
    deletedBy: MetaUser | null;
    createdAt: Date;
    updatedAt: Date | null;
    deletedAt: Date | null;
}


interface IQueryFilter {
    text?: string;
    createdAt?: Date;
    updatedAt?: Date;
    deletedAt?: Date;
}



interface NewBaseMeta {
    c_by: number;
    u_by: number | null;
    d_by: number | null;
    c_at: Date;
    u_at: Date | null;
    d_at: Date | null;
}

interface NewParsedMeta {
    c_by: ActionByInfo;
    u_by: ActionByInfo | null;
    d_by: ActionByInfo | null;
    c_at: Date;
    u_at: Date | null;
    d_at: Date | null;
}



export { InterfaceMetaData, APIBaseResponse, PaginatedBaseResponse, AuditMetaKeys, AuditData, DTO, BaseMeta, ParsedMeta, MetaUser, BaseMetaUsers, IQueryFilter,NewParsedMeta ,NewBaseMeta,ActionByInfo,   
};