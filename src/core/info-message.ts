const InfoMessage={
    create:'Data successfully created.',
    
    update:'Data successfully updated.',
    
    get:'Data successfully fetched.',
    
    delete:'Data successfully deleted.',
    
    notCreate:'Data not created.',
    
    notUpdate:'Data not updated.',
    
    notExist:'Data not exist.',
    
    notDelete:'Data not deleted.',

    errorNotCreate:'Something went wrong : Data not created.',

    errorNotUpdate:'Something went wrong : Data not updated.',

    errorNotFetched:'Something went wrong : Data not fetched.',

    errorNotDelete:'Something went wrong : Data not deleted.',
    
}
export default InfoMessage


export enum HTTP_STATUS {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
}