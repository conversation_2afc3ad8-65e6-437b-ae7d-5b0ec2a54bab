import { ValidationError, UniqueConstraintError, DatabaseError } from "sequelize";

/**
 * Handles various Sequelize errors and returns a standardized error response
 * @param {unknown} error - The error object thrown by Sequelize
 * @param {string} [message] - Optional custom error message for unexpected errors
 * @returns {{
 *   status: number,
 *   message: string,
 *   details?: any
 * }} Standardized error response object with status code, message, and optional details
 * 
 * @throws {ValidationError} Returns 400 status with validation error details
 * @throws {UniqueConstraintError} Returns 409 status with constraint violation details
 * @throws {DatabaseError} Returns 500 status with database error details
 * @throws {Error} Returns 500 status for unexpected errors
 */
export function sequlizeErrorHandler(error: unknown, message?: string): {
  status: number;
  message: string;
  details?: any;
} {
  if (error instanceof UniqueConstraintError) {
    return {
      status: 409,
      message: "Unique constraint violation",
      details: error.errors.map(err => (err.message)).join(', or ')
    };
  }
  if (error instanceof ValidationError) {
    return {
      status: 400,
      message: "Validation error",
      details: error.errors.map(err => (err.message)).join(', or ')
    };
  }

  if (error instanceof DatabaseError) {
    return {
      status: 500,
      message: "Database error",
      details: error.message
    };
  }

  return {
    status: 500,
    message: "An unexpected error occurred",
    details: message
  };
}

export default sequlizeErrorHandler