type DTO<T, E = unknown> = SuccessDTO<T> | FailedDTO<E>;

enum DTO_CODE {
    SUCCESS,
    HANDLED_ERROR,
    UNHANDLED_ERROR,
}

interface SuccessDTO<T> {
    success: true;
    code: DTO_CODE.SUCCESS;
    message: string | null;
    data: T;
    statusCode: number | null;
}

/**
 * code 1 mean handled, code 2 means need to handle
 */
interface FailedDTO<E> {
    code: DTO_CODE.HANDLED_ERROR | DTO_CODE.UNHANDLED_ERROR;
    success: false;
    message: string;
    data: null;
    error: E | null;
    statusCode: number | null;

}

// Your class with static methods that return the correct types
const DTO = {
    Success<T>(data: T, statusCode: number | null = null, message: string | null = null): SuccessDTO<T> {
        const res: SuccessDTO<T> = {
            success: true,
            code: DTO_CODE.SUCCESS,
            message,
            data,
            statusCode: statusCode ?? 200,
        };
        return res;
    },

    HandledError<E>(message: string, statusCode: number | null = null, error: E | null = null): FailedDTO<E> {
        const dto: FailedDTO<E> = {
            success: false,
            code: DTO_CODE.HANDLED_ERROR,
            message,
            data: null,
            error,
            statusCode: statusCode ?? 500,

        };
        return dto;
    },

    UnhandledError<E>(message: string, statusCode: number | null = null, error: E | null = null): FailedDTO<E> {
        const dto: FailedDTO<E> = {
            success: false,
            code: DTO_CODE.UNHANDLED_ERROR,
            message,
            data: null,
            error,
            statusCode: statusCode ?? 500,
        };
        return dto;
    },
};

/* -------------------------------------------------------------------------- */
/*                                    Functions                               */
/* -------------------------------------------------------------------------- */

const getSuccessDTO = <T, E = unknown>(data: T, statusCode: number | null = null, message: string | null = null): DTO<T, E> => {
    return DTO.Success(data, statusCode, message);
};

const getHandledErrorDTO = <E>(message: string, statusCode: number | null = null, error: E | null = null): FailedDTO<E> => {
    return DTO.HandledError(message, statusCode, error);
};

const getUnhandledErrorDTO = <E>(message: string, statusCode: number | null = null, error: E | null = null): FailedDTO<E> => {
    return DTO.UnhandledError(message, statusCode, error);
};


const isHandledError = <E>(dto: FailedDTO<E>) => {
    return dto.code === DTO_CODE.HANDLED_ERROR

}

const isUnhandledError = <E>(dto: FailedDTO<E>) => {
    return dto.code === DTO_CODE.UNHANDLED_ERROR
}

export { getSuccessDTO, getUnhandledErrorDTO, getHandledErrorDTO, DTO_CODE, DTO, isUnhandledError, isHandledError };