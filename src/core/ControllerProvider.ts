import ItemUnitEntityRelationController from "../features/entity_relations/item-unit-entity-relation/controller/ItemUnitEntityRelationController";
import FgStockInController from "../features/fg-stock-in/controller/FgStockInContoller";
import FgStockIssueController from "../features/fg-stock-issue/controller/FgStockIssue";
import WarehouseController from "../features/warehouse/controller/warehouse-controller";

class ControllerProvider {
    private static _fgStockIn: FgStockInController
    private static _fgStockIssue: FgStockIssueController
    private static _warehouse: WarehouseController
    private static _itemUnitEntityRelation: ItemUnitEntityRelationController

    static get fgStockIn() {
        if (!this._fgStockIn) this._fgStockIn = new FgStockInController()

        return this._fgStockIn
    }

    static get fgStockIssue() {
        if (!this._fgStockIssue) this._fgStockIssue = new FgStockIssueController()

        return this._fgStockIssue
    }


    static get warehouse() {
        if (!this._warehouse) this._warehouse = new WarehouseController()

        return this._warehouse
    }

    static get itemUnitEntityRelation() {
        if (!this._itemUnitEntityRelation) this._itemUnitEntityRelation = new ItemUnitEntityRelationController()

        return this._itemUnitEntityRelation
    }
}

export default ControllerProvider