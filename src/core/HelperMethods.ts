import { pick } from 'lodash';
import { APIBaseResponse } from "./CoreInterfaces";
import { DTO } from './DTO';
import sequlize<PERSON>rror<PERSON>andler from './sequlize-error-handler';

export class HelperMethods {

    static getSuccessResponse<T>(data: T, message?: string): APIBaseResponse<T> {
        return {
            success: true,
            message: message ?? "Done",
            data: data,
        };
    }

    static getErrorResponse(message?: string): APIBaseResponse<null> {
        return {
            success: false,
            message: message ?? "Something went wrong",
            data: null,
        };
    }

    static getUnAuthorisedResponse(message?: string): APIBaseResponse<null> {
        return {
            success: false,
            message: message ?? "Unauthorised",
            data: null,
        };
    }
    static handleError(error: any, message?: string) {
        console.trace(error);
        const err= sequlizeErrorHandler(error, message ? message : error && error.message ? error.message : "unhandled internal error");
        return DTO.UnhandledError(err.details, error);
    }


    static pickInterfaceFields<T, K extends keyof T>(obj: any, keys: string[]): Pick<T, K> {
        if (!obj || typeof obj !== 'object') {
            return {} as Pick<T, K>;
        }
        return pick(obj, keys) as Pick<T, K>;
    }

    /**
    * Creates a validation error response object
    * @template T Type of the validation error data
    * @param {T} errorData The validation error details
    * @returns {APIBaseResponse<null|T>} A standardized validation error response object with status 400
    */
    static ValidationError<T>(errorData: T): APIBaseResponse<null | T> {
        return {
            success: false,
            message: (errorData[0] as any).message ?? 'Something went wrong',
            data: errorData,
        }
    }

    static isError = (error: any) => error instanceof Error

}