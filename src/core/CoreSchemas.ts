import { z } from "zod";

export class CoreSchemas {

    static paginationSchema = z.object({
        page: z.string().optional()
            .refine(val => val && val.trim() !== "", {
                message: "Page parameter is required",
            }).transform(Number)
            .refine(val => !isNaN(val), {
                message: "Page must be a number",
            })
        ,
        pageSize: z.string().optional()
            .refine(val => val && val.trim() !== "", {
                message: "Page Size parameter is required",
            }).transform(Number)
            .refine(val => !isNaN(val), {
                message: "Page size must be a number",
            }),
        text: z.string().optional(),
    });

    static searchByTextSchema = z.object({
        text: z.string().min(3, "Please enter at least 3 characters").optional()
            .refine(val => val !== undefined, {
                message: "\"text\" parameter is required",
            }),
    });

    static updateSchema = z.object({
        id: z.number().int().positive("Invalid update request"),
    });

    static getByIdSchema = z.object({
        id: z.string().transform(Number).refine(val => !isNaN(val) && val > 0, {
            message: "Id must be a positive number",
        }),
    });

    static updateByIdSchema = z.object({
        id: z.string().transform(Number).refine(val => !isNaN(val) && val > 0, {
            message: "Id must be a positive number",
        }),
    });

    static deleteByIdSchema = z.object({
        id: z.string().transform(Number).refine(val => !isNaN(val) && val > 0, {
            message: "Id must be a positive number",
        }),
    });
    static getIdArraySchema = z.object({
        ids: z.array(z.number().refine(val => !isNaN(val) &&
            val > 0, {
            message: "Id must be a positive number",
        })),
    });

    static newPaginationSchema = {
        page: z.string().optional()
            .refine(val => val && val.trim() !== "", {
                message: "Page parameter is required",
            }).transform(Number)
            .refine(val => !isNaN(val), {
                message: "Page must be a number",
            }).optional()
        ,
        page_size: z.string().optional()
            .refine(val => val && val.trim() !== "", {
                message: "Page Size parameter is required",
            }).transform(Number)
            .refine(val => !isNaN(val), {
                message: "Page size must be a number",
            }).optional(),
        order: z.enum(["ASC", "DESC"], {
            errorMap: () => ({ message: "Order must be either 'ASC' or 'DESC'." }),
        }).optional(),
        search: z.string().optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
    }
}