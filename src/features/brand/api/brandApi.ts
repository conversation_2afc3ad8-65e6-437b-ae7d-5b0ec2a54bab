import * as express from "express";

import { validateCreateBrand, validateDeleteBrand, validateBrandId, validateUpdateBrand, validatePaginationBrand } from "../validation/brand";
import { BrandController } from "../controllers/brandController";

const brandController =new BrandController()

const brandRouter = express.Router();
const apiInitialPath='/brand'

brandRouter.get(`${apiInitialPath}`,validatePaginationBrand, brandController.getAll);
brandRouter.get(`${apiInitialPath}/:id`, validateBrandId, brandController.get);
brandRouter.post(`${apiInitialPath}`, validateCreateBrand, brandController.create);
brandRouter.put(`${apiInitialPath}/:id`, validateBrandId, validateUpdateBrand, brandController.update);
brandRouter.delete(`${apiInitialPath}/:id`, validateDeleteBrand, brandController.delete);


export default brandRouter;



