import { ERROR_MESSAGE } from "../../../core/constants";
import { RepoProvider } from "../../../core/RepoProvider";
import { BrandPayload, IBrandFilter } from "../interface/brand";
import { sequelizeInit } from "../../../sequelize_init";
import { Request, Response } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { get } from "lodash";
import { allowSync, Entity, Permission } from "../../../utils/AllowSync";
import { Transaction } from "sequelize";

export class BrandController {

    async create(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const user_id = get(req, "user_id")!;
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const payload: BrandPayload = req.body;
            const brand = await RepoProvider.brandRepo.createBrand(payload, user_id, transaction);
            if (!brand.success) {
                throw new Error(brand.message)
            }
            const canSync = allowSync(Entity.BRAND, Permission.CREATE)
            if (canSync) {
                await BrandController.syncBranchWithCrm(payload, user_id, transaction)
            }
            await transaction.commit();
            res.status(201).send(brand);
        } catch (error) {
            await transaction.rollback();

            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    async get(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const brandId = req.params.id;
            const brand = await RepoProvider.brandRepo.getBrand(+brandId, transaction);
            if (!brand.success) {
                throw new Error(brand.message)
            }
            await transaction.commit();
            res.status(200).send(brand);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    async getAll(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            let size = Number(get(req.query, "pageSize"));
            const page = Number(get(req.query, "page"));

            const rawStartDate = get(req.query, 'startDate');
            const rawEndDate = get(req.query, 'endDate');
            const rawSearch = get(req.query, 'search');
            const rawOrder = get(req.query, 'order');

            const startDate = typeof rawStartDate === 'string' ? new Date(rawStartDate) : undefined;
            const endDate = typeof rawEndDate === 'string' ? new Date(rawEndDate) : undefined;
            const search = typeof rawSearch === 'string' ? rawSearch : undefined;
            const order = rawOrder === 'DESC' ? 'DESC' : 'ASC';

            const filters: IBrandFilter = { startDate, endDate, search, order };

            if (size && !page) {
                throw new Error('Page number is required')
            }
            if (page && !size) {
                size = 10
            }

            const brands = await RepoProvider.brandRepo.getAllBrands(page, size, filters, transaction);
            if (!brands.success) {
                throw new Error(brands.message)
            }
            await transaction.commit();
            res.status(200).send(brands);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    async update(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const user_id = get(req, "user_id")!;
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const brandId = req.params.id;
            const payload: BrandPayload = req.body;
            const updatedBrand = await RepoProvider.brandRepo.updateBrand(+brandId, payload, user_id, transaction);
            if (!updatedBrand.success) {
                throw new Error(updatedBrand.message)
            }
            const canSync = allowSync(Entity.BRAND, Permission.UPDATE)
            if (canSync) {
                await BrandController.syncUpdateBrandWithCrm(+brandId, payload, user_id, transaction)
            }
            await transaction.commit();
            res.status(200).send(updatedBrand);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    async delete(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const user_id = get(req, "user_id")!;
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const brandId = req.params.id;
            const deleted = await RepoProvider.brandRepo.deleteBrand(+brandId, user_id, transaction);
            if (!deleted.success) {
                throw new Error(deleted.message)
            }
            await transaction.commit();
            res.status(200).send(deleted);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    static async syncBranchWithCrm(payload: BrandPayload, userId: number, transaction: Transaction) {
        try {
            const syncBrandCrm = await RepoProvider.brandEntityRelationRepo.createBrandFromErp(payload, userId, transaction)
            if (!syncBrandCrm.success) {
                throw new Error(syncBrandCrm.message)
            }
        } catch (error) {
            HelperMethods.handleError(error)
        }
    }

    static async syncUpdateBrandWithCrm(id: number, payload: BrandPayload, userId: number, transaction: Transaction){
        try {
            const syncBrandCrm = await RepoProvider.brandEntityRelationRepo.updateBrandFromErp(id, payload, userId, transaction)
            if (!syncBrandCrm.success) {
                throw new Error(syncBrandCrm.message)
            }
        } catch (error) {
            HelperMethods.handleError(error)
        }
    }

  
}

