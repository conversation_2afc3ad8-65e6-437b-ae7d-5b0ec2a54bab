import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { RepoProvider } from "../../../core/RepoProvider";
import { PaginationProvider } from "../../pagination/PaginationProvider";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { BrandPayload, IBrandFilter, ParsedBrand } from "../interface/brand";
import { BrandTableModel } from "../model/brandTable";
import parsedBrand from "../parser/brandParser";
import { IBrandRepo } from "./IBrandRepo";
import { Includeable, Op, Transaction } from "sequelize";

export class BrandRepo implements IBrandRepo {
    async createBrand(brand: BrandPayload, userId: number, transaction: Transaction): Promise<APIBaseResponse<BrandTableModel | null>> {
        try {

            const isBrandExists = await BrandTableModel.findOne({
                where: {
                    title: brand.title,
                },
                transaction
            });
            if (isBrandExists) {
                throw new Error(`Brand already exists of this (${brand.title}) name `)
            }

            const createdBrand = await BrandTableModel.create({
                title: brand.title,
                description: brand.description,
                logo: brand.logo,
                whiteListedDomains: brand.whiteListedDomains,
                createdById: userId,
            }, { transaction, userId: userId });

            return HelperMethods.getSuccessResponse(createdBrand, 'Brand successfully created.')
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not created.')
        }
    }

    async getAllBrands(page: number, limit: number, filters: IBrandFilter, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<BrandTableModel> | null>> {
        try {

            const where: any = {}
            if (filters.search) {
                where[Op.or] = [
                    { title: { [Op.iLike]: `%${filters.search}%` } },
                ];
            }
            const include: Includeable[] = [
                {
                    model: CoreUserTable,
                    as: "createdByUser",
                    attributes: ["id", "firstName", "lastName", "email"],
                },
                {
                    model: CoreUserTable,
                    as: "updatedByUser",
                    attributes: ["id", "firstName", "lastName", "email"],
                },
                {
                    model: CoreUserTable,
                    as: "deletedByUser",
                    attributes: ["id", "firstName", "lastName", "email"],
                },
            ]
            const startDate = filters.startDate
            const endDate = filters.endDate
            const order: [string, "ASC" | "DESC"][] = [
                ["id", filters.order ?? "ASC"]
            ];
            if (!limit) {
                limit = -1
            }
            const paginatedData = await new PaginationProvider<any, BrandTableModel>().getPaginatedRecords(BrandTableModel, { include: include, limit: limit, page: page, where: where, dateColumn: "createdAt",order,startDate,endDate }, transaction)
            if (paginatedData.rows.length === 0) {
                return HelperMethods.getErrorResponse('Data not exists')
            }
            return HelperMethods.getSuccessResponse({
                totalData: paginatedData.total,
                data: paginatedData.rows,
                totalPages: paginatedData.totalPages,
                currentPage: paginatedData.currentPage,
            }, 'Data fetched successfully')
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not fetched.')
        }
    }

    async getBrand(id: number, transaction: Transaction): Promise<APIBaseResponse<ParsedBrand | null>> {
        try {
            const allBrands = await BrandTableModel.findOne({
                where: {
                    id: id,
                },
                include: [
                    {
                        model: CoreUserTable,
                        as: "createdByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                    {
                        model: CoreUserTable,
                        as: "updatedByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                    {
                        model: CoreUserTable,
                        as: "deletedByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                ],
                transaction
            })

            if (!allBrands) {
                throw new Error('Data not exists')
            }
            return HelperMethods.getSuccessResponse(parsedBrand(allBrands.toJSON()), 'Data fetched successfully')
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not fetched.')
        }
    }

    async updateBrand(id: number, brand: BrandPayload, userId: number, transaction: Transaction): Promise<APIBaseResponse<BrandTableModel | null>> {
        try {
            const isBrandExists = await BrandTableModel.findByPk(id, { transaction });
            if (!isBrandExists) {
                throw new Error("Brand not found");
            }
            const updatedBrand = await BrandTableModel.update({
                title: brand.title,
                description: brand.description,
                logo: brand.logo,
                updatedById: userId
            }, {
                where: {
                    id: id,
                },
                returning: true,
                transaction,
                userId
            });
            if (updatedBrand[0] === 0) {
                throw new Error(`Data not updated of this id (${id})`)
            }
            const data = await BrandTableModel.findByPk(id, { transaction });
            return HelperMethods.getSuccessResponse(data)
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not update.')
        }
    }

    async deleteBrand(id: number, userId: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            const isBrandExists = await BrandTableModel.findByPk(id, { transaction });
            if (!isBrandExists) {
                throw new Error("Brand not found");
            }
            const deletedBrand = await BrandTableModel.destroy({
                where: {
                    id: id,
                },
                transaction,
                userId: userId
            });
            if (deletedBrand === 0) {
                throw new Error(`Data not deleted of this id (${id})`)
            }
            return HelperMethods.getSuccessResponse(null, `Data successfully deleted of this id (${id})`)
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not delete.')
        }
    }

    async getBrandData(arrayIds: number[], currentBrandId: number, transaction: Transaction): Promise<APIBaseResponse<ParsedBrand | null>> {
        try {
            const currentBrand = arrayIds.find((id) => id === currentBrandId);

            const brandData = await BrandTableModel.findOne({ where: { id: currentBrand }, transaction })
            if (!brandData) {
                throw new Error('Data not exists')
            }
            return HelperMethods.getSuccessResponse(parsedBrand(brandData), 'Data fetched successfully')
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not fetched.')
        }

    }
}
