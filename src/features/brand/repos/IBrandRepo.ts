import { Transaction } from "sequelize";
import { BrandPayload, ParsedBrand } from "../interface/brand";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { BrandTableModel } from "../model/brandTable";

export interface IBrandRepo {
    getAllBrands(page: number, limit: number, filters: any, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<BrandTableModel> | null>>;
    getBrand(id: number, transaction: Transaction): Promise<APIBaseResponse<ParsedBrand | null>> ;
    createBrand(payload:BrandPayload, user_id:number, transaction: Transaction):Promise<APIBaseResponse<BrandTableModel | null>>;
    updateBrand(id: number, payload: BrandPayload, userId:number, transaction: Transaction):Promise<APIBaseResponse<BrandTableModel | null>> ;
    deleteBrand(id: number,userId:number, transaction: Transaction): Promise<APIBaseResponse<null>> ;
    getBrandData(arrayIds: number[], currentBrandId: number,transaction:Transaction):Promise<APIBaseResponse<ParsedBrand | null>>;
}