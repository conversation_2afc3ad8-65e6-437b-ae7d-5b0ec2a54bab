import { InterfaceMetaData } from "../../../core/CoreInterfaces";


interface BrandPayload {
    title: string,
    description: string,
    logo: string,
    whiteListedDomains: string[],
    reference_obj?: JSON | null;
    reference_str?: string | null;
    reference_int?: number | null;
}

interface IBrandTable extends BrandPayload, InterfaceMetaData {

}
interface IBrandCreationAttributes extends BrandPayload {
    createdById: number
}

interface ParsedBrand extends InterfaceMetaData {
    whiteListedDomains: string[],
    title: string,
    description: string,
    logo: string,
}
interface IBrandFilter {
    search: string |undefined,
    startDate:Date|undefined,
    endDate:Date|undefined,
    order:"DESC"|"ASC"
}

export {
    IBrandTable,
    BrandPayload,
    ParsedBrand,
    IBrandCreationAttributes,
    IBrandFilter
}
