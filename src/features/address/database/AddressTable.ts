import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { IAddress, ICreateAddress } from '../models/IAddress';
import { ADDRESS_STATUS } from '../models/AddressMisc';
import { RepoProvider } from '../../../core/RepoProvider';
import { NormalUserTable } from '../../users/sub_feaures/normal_user/database/NormalUserTable';


class AddressTable extends Model<IAddress, ICreateAddress> {
    static initModel() {
        AddressTable.init(
            {
                id: {
                    type: DataTypes.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                    get() {
                        const value = this.dataValues.id;
                        if (value) {
                            return Number(value);
                        }
                    },
                },
                street: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                city: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                state: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                country: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                postalCode: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                status: {
                    type: DataTypes.ENUM(...Object.values(ADDRESS_STATUS)),
                    allowNull: false,
                },

                createdById: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                    get() {
                        const value = this.dataValues.createdById;
                        if (value) {
                            return Number(value);
                        }
                    },
                },
                createdAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                },
                updatedById: {
                    type: DataTypes.INTEGER,
                    get() {
                        const value = this.dataValues.updatedById;
                        if (value) {
                            return Number(value);
                        }
                    },
                },
                updatedAt: {
                    type: DataTypes.DATE,
                },
                deletedById: {
                    type: DataTypes.INTEGER,
                    get() {
                        const value = this.dataValues.deletedById;
                        if (value) {
                            return Number(value);
                        }
                    },
                },
                deletedAt: {
                    type: DataTypes.DATE,
                }
            },
            {
                sequelize: sequelizeInit,
                tableName: 'address',
                timestamps: true,
                paranoid: true,
            },
        );
        return AddressTable
    }

    static associate = () => {
        AddressTable.hasMany(NormalUserTable, {
            foreignKey: 'addressId',
        })
        NormalUserTable.belongsTo(AddressTable, {
            foreignKey: 'addressId',
            as: 'address',
        })
    }

    static hooks = () => {
        AddressTable.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "Address",
                instance,
                options
            );
        });

        AddressTable.addHook("afterUpdate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "update",
                "Address",
                instance,
                options
            );
        });

        AddressTable.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "Address",
                instance,
                options
            );
        });
    }

}



export { AddressTable };