import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { PurchaseInvoiceTable } from "../../purchase_invoice/database/PurchaseInvoiceTable";
import { RawMaterialPriceTable } from "../../raw_material/database/RawMaterialPriceTable";
import { RawMaterialVariationTable } from "../../raw_material/database/RawMaterialVariationTable";
import { RawMaterialRejectionTable } from "../../raw_material_stock/database/RawMaterialRejectionTable";
import { RawMaterialStockInTable } from "../../raw_material_stock/database/RawMaterialStockInTable";
import { RawMaterialStockTable } from "../../raw_material_stock/database/RawMaterialStockTable";
import { ICreateRawMaterialRejection } from "../../raw_material_stock/models/IRawMaterialRejection";
import { SupplierTable } from "../../supplier/database/SupplierTable";
import { DebitNoteTable } from "../database/DebitNoteTable";
import { DEBIT_NOTE_SOURCE, DEBIT_NOTE_STATUS } from "../models/DebitNoteMisc";
import { ICreateDebitNote, ICreateDebitNotesData, IDebitNoteData, IDebitNoteOverviewResponse, IDebitNoteResponse, IMarkDebitNotePaid, IUpdateDebitNote } from "../models/IDebitNote";
import { IDebitNoteRepo } from "./IDebitNoteRepo";
import { Op, Transaction, UniqueConstraintError, } from "sequelize";

export class PostgresDebitNoteRepo implements IDebitNoteRepo {

    async create(payload: ICreateDebitNote, transaction: Transaction): Promise<APIBaseResponse<DebitNoteTable | null>> {
        try {
            const result = await DebitNoteTable.create(payload, {
                userId: payload.createdById,
                transaction
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Category already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async markPaid(payload: IMarkDebitNotePaid, transaction: Transaction): Promise<APIBaseResponse<null>> {

        try {
            await DebitNoteTable.update({
                status: DEBIT_NOTE_STATUS.PAID,
                updatedById: payload.updatedId,
                note: payload.note,
            },
                {
                    where: {
                        purchaseInvoiceId: payload.purchaseInvoiceId
                    },
                    userId: payload.updatedId,
                    individualHooks: true,
                    transaction
                }
            );

            return HelperMethods.getSuccessResponse(null);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IDebitNoteOverviewResponse> | null>> {
        try {
            const offset = (page - 1) * pageSize;


            /* get distinct data */

            const distinctData = await DebitNoteTable.count({
                distinct: true,
                attributes: ['purchaseInvoiceId'],
                group: ['purchaseInvoiceId'],
                where: {
                    status: DEBIT_NOTE_STATUS.UNPAID
                },
                transaction
            });


            const { count, rows } = await DebitNoteTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],

                where: {
                    status: DEBIT_NOTE_STATUS.UNPAID
                },
                transaction,
                include:
                    [
                        {
                            model: PurchaseInvoiceTable,
                            as: 'purchaseInvoice',
                            required: true,
                            include: [
                                {
                                    model: SupplierTable,
                                    as: "supplier",
                                    required: true,
                                }
                            ],

                        },
                        {
                            model: RawMaterialVariationTable,
                            as: 'rawMaterial',
                            required: true,
                        },
                    ],

            });

            const data: IDebitNoteOverviewResponse[] = [];

            let debitAmount = 0;

            let groupedData: DebitNoteTable[] = [];

            let dataToIterate: DebitNoteTable[] = [];

            let fetchedItem: DebitNoteTable;


            dataToIterate = rows;

            for (const fetchedItem of dataToIterate) {


                debitAmount = 0;

                /* find all for a single */

                groupedData = dataToIterate.filter(data => data.dataValues.purchaseInvoiceId === fetchedItem.dataValues.purchaseInvoiceId);


                /* calculate  */
                for (const item of groupedData) {

                    if (item.dataValues.source === DEBIT_NOTE_SOURCE.REJECTED) {
                        debitAmount = Number((debitAmount + ((item.dataValues.purchasedPrice) * item.dataValues.qty)).toFixed(2));

                    }
                    else {


                        debitAmount = Number((debitAmount + ((item.dataValues.purchasedPrice - item.dataValues.actualPrice) * item.dataValues.qty)).toFixed(2));
                    }
                }



                if (groupedData.length > 0) {
                    /* add data */
                    data.push({
                        purchaseInvoiceId: fetchedItem.dataValues.purchaseInvoiceId,
                        debitAmount: debitAmount,
                        purchaseInvoice: fetchedItem.purchaseInvoice.dataValues.invoiceNumber,
                        purchaseInvoiceDate: fetchedItem.purchaseInvoice.dataValues.invoiceDate,
                        supplier: fetchedItem.purchaseInvoice.supplier.dataValues.name,
                        source: fetchedItem.dataValues.source,
                    });
                }



                /*delete */
                dataToIterate = dataToIterate.filter(data => data.dataValues.purchaseInvoiceId !== fetchedItem.dataValues.purchaseInvoiceId);

            }





            const totalPages = Math.ceil(distinctData.length / pageSize);


            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: distinctData.length,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByText(text: string, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IDebitNoteOverviewResponse> | null>> {
        try {


            // /* get suppliers */
            // const suppliers = await SupplierTable.findAll({
            //     where: {
            //         name: {
            //             [Op.iLike]: `%${text}%`
            //         }
            //     },
            // });

            /* get invoice numbers */
            const invoicenumbers = await PurchaseInvoiceTable.findAll({
                where: {
                    poNumber: {
                        [Op.iLike]: `%${text}%`
                    }
                },
                transaction
            });


            /* get distinct data */

            const distinctData = await DebitNoteTable.count({
                distinct: true,
                attributes: ['purchaseInvoiceId'],
                group: ['purchaseInvoiceId'],
                where: {

                    purchaseInvoiceId: {
                        [Op.in]: invoicenumbers.map((item) => item.dataValues.id)
                    },

                    status: DEBIT_NOTE_STATUS.UNPAID
                },
                transaction
            });


            const { count, rows } = await DebitNoteTable.findAndCountAll({
                limit: 5,
                order: [['createdAt', 'DESC']],

                where: {
                    purchaseInvoiceId: {
                        [Op.in]: invoicenumbers.map((item) => item.dataValues.id)
                    },
                    status: DEBIT_NOTE_STATUS.UNPAID
                },
                transaction,
                include:
                    [
                        {
                            model: PurchaseInvoiceTable,
                            as: 'purchaseInvoice',
                            required: true,
                            include: [
                                {
                                    model: SupplierTable,
                                    as: "supplier",
                                    required: true,
                                }
                            ],

                        },
                        {
                            model: RawMaterialVariationTable,
                            as: 'rawMaterial',
                            required: true,
                        },
                    ],

            });

            const data: IDebitNoteOverviewResponse[] = [];

            let debitAmount = 0;

            let groupedData: DebitNoteTable[] = [];

            let dataToIterate: DebitNoteTable[] = [];

            let fetchedItem: DebitNoteTable;


            dataToIterate = rows;

            for (const fetchedItem of dataToIterate) {


                if (!fetchedItem.purchaseInvoice.dataValues.invoiceNumber.includes(text)) {
                    continue;
                }

                debitAmount = 0;

                /* find all for a single */

                groupedData = dataToIterate.filter(data => data.dataValues.purchaseInvoiceId === fetchedItem.dataValues.purchaseInvoiceId);


                /* calculate  */
                for (const item of groupedData) {

                    if (item.dataValues.source === DEBIT_NOTE_SOURCE.REJECTED) {
                        debitAmount = Number((debitAmount + ((item.dataValues.actualPrice) * item.dataValues.qty)).toFixed(2));

                    }
                    else {


                        debitAmount = Number((debitAmount + ((item.dataValues.purchasedPrice - item.dataValues.actualPrice) * item.dataValues.qty)).toFixed(2));
                    }
                }



                if (groupedData.length > 0) {
                    /* add data */
                    data.push({
                        purchaseInvoiceId: fetchedItem.dataValues.purchaseInvoiceId,
                        debitAmount: debitAmount,
                        purchaseInvoice: fetchedItem.purchaseInvoice.dataValues.invoiceNumber,
                        purchaseInvoiceDate: fetchedItem.purchaseInvoice.dataValues.invoiceDate,
                        supplier: fetchedItem.purchaseInvoice.supplier.dataValues.name,
                        source: fetchedItem.dataValues.source,
                    });
                }



                /*delete */
                dataToIterate = dataToIterate.filter(data => data.dataValues.purchaseInvoiceId !== fetchedItem.dataValues.purchaseInvoiceId);

            }





            const totalPages = 1;


            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: distinctData.length,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }



    async getByPurchasInvoiceId(purchaseInvoiceId: number, transaction: Transaction): Promise<APIBaseResponse<IDebitNoteResponse[] | null>> {
        try {
            const result = await DebitNoteTable.findAll({

                where: {
                    purchaseInvoiceId: purchaseInvoiceId,
                },
                transaction,
                include:
                    [
                        {
                            model: PurchaseInvoiceTable,
                            as: 'purchaseInvoice',
                            include: [
                                {
                                    model: SupplierTable,
                                    as: "supplier"
                                },
                                {
                                    model: RawMaterialRejectionTable,
                                    as: "rawMaterialRejections"
                                }
                            ],

                        },
                        {
                            model: RawMaterialVariationTable,
                            as: 'rawMaterial',
                        },
                    ]
            });

            const data: IDebitNoteResponse[] = [];

            let debitAmount = 0;

            for (const item of result) {
                debitAmount = 0;

                if (item.dataValues.source === DEBIT_NOTE_SOURCE.REJECTED) {
                    debitAmount = Number((debitAmount + ((item.dataValues.purchasedPrice) * item.dataValues.qty)).toFixed(2));

                }

                else {

                    debitAmount = Number(((item.dataValues.purchasedPrice - item.dataValues.actualPrice) * item.dataValues.qty).toFixed(2));
                }


                data.push({
                    id: item.dataValues.id,
                    purchaseInvoiceId: item.dataValues.purchaseInvoiceId,
                    actualPrice: item.dataValues.actualPrice,
                    purchasedPrice: item.dataValues.purchasedPrice,
                    status: item.dataValues.status,
                    debitAmount: debitAmount,
                    qty: item.dataValues.qty,
                    purchaseInvoice: item.purchaseInvoice.dataValues.invoiceNumber,
                    purchaseInvoiceDate: item.purchaseInvoice.dataValues.invoiceDate,
                    rawMaterial: item.rawMaterial.dataValues.name,
                    supplier: item.purchaseInvoice.supplier.dataValues.name,
                    source: item.dataValues.source,
                    rejectionReason: item.purchaseInvoice.rawMaterialRejections.find(data => data.dataValues.rawMaterialId === item.dataValues.rawMaterialId)?.dataValues.rejectionReason ?? null,
                });

            }
            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async createDebitNotes(payload: ICreateDebitNotesData, transaction: Transaction): Promise<APIBaseResponse<DebitNoteTable | null>> {
        let responseData: DebitNoteTable | null = null;
        const rejectionData: ICreateRawMaterialRejection[] = [];
        const debitNotesToCreate: any[] = [];

        try {
            // Get purchaseInvoice inside the transaction
            const purchaseInvoice = await PurchaseInvoiceTable.findByPk(payload.purchaseInvoiceId, { transaction });
            if (!purchaseInvoice) {
                if (transaction) await transaction.rollback();
                return HelperMethods.getErrorResponse("Purchase invoice not found");
            }

            for (const debitNote of payload.debitNotes) {
                // Fetch previous manual debit notes
                const [previousDebitNotes, checkDebitNote, rawMaterialStock, rawMaterialStockIn] = await Promise.all([
                    DebitNoteTable.findAll({
                        where: {
                            purchaseInvoiceId: payload.purchaseInvoiceId,
                            rawMaterialId: debitNote.rawMaterialId,
                            is_manual: true,
                        },
                        attributes: ["qty"],
                        transaction,
                    }),
                    DebitNoteTable.findOne({
                        where: {
                            purchaseInvoiceId: payload.purchaseInvoiceId,
                            rawMaterialId: debitNote.rawMaterialId,
                            is_manual: false,
                        },
                        transaction,
                    }),
                    RawMaterialStockTable.findOne({
                        where: { rawMaterialId: debitNote.rawMaterialId },
                        transaction,
                    }),
                    RawMaterialStockInTable.findOne({
                        where: {
                            purchaseInvoiceId: purchaseInvoice.dataValues.id,
                            rawMaterialId: debitNote.rawMaterialId,
                            supplierId: purchaseInvoice.dataValues.supplierId
                        },
                        transaction
                    }),
                ]);

                // Calculate total previously rejected quantity
                const totalOldRejectedQty = previousDebitNotes.reduce((sum, note) => sum + note.dataValues.qty, 0);
                const totalNewRejectedQty = totalOldRejectedQty + debitNote.rejectedQty;

                // Validate total rejected quantity ≤ stockInQty
                if (rawMaterialStockIn && totalNewRejectedQty > rawMaterialStockIn.dataValues.qty) {
                    if (transaction) await transaction.rollback();
                    return HelperMethods.getErrorResponse("Total rejected quantity exceeds stock-in quantity");
                }

                // Check stock availability
                if (!rawMaterialStock || rawMaterialStock.dataValues.totalStock < debitNote.rejectedQty) {
                    if (transaction) await transaction.rollback();
                    return HelperMethods.getErrorResponse("Total Stock is less than the rejected quantity");
                }

                // Update stock
                const stockUpdateResult = await this.updateStock(
                    rawMaterialStockIn!,
                    rawMaterialStock,
                    debitNote.rawMaterialId,
                    debitNote.rejectedQty,
                    purchaseInvoice,
                    transaction
                );
                if (!stockUpdateResult?.success) {
                    if (transaction) await transaction.rollback();
                    return HelperMethods.getErrorResponse(stockUpdateResult?.message);
                }

                // Determine price
                const actualPrice = checkDebitNote?.dataValues.actualPrice || (await RawMaterialPriceTable.findOne({
                    where: {
                        rawMaterialId: debitNote.rawMaterialId,
                        supplierId: purchaseInvoice.dataValues.supplierId,
                    },
                    transaction,
                }))?.dataValues.price || 0;

                const purchasedPrice = checkDebitNote?.dataValues.purchasedPrice || actualPrice;

                if (actualPrice == 0 && purchasedPrice == 0) {
                    if (transaction) await transaction.rollback();
                    return HelperMethods.getErrorResponse("Actual Price and Purchased Price cannot be zero");
                }

                // Prepare data for bulk insert
                debitNotesToCreate.push({
                    purchaseInvoiceId: payload.purchaseInvoiceId,
                    rawMaterialId: debitNote.rawMaterialId,
                    source: checkDebitNote
                        ? checkDebitNote.dataValues.source === DEBIT_NOTE_SOURCE.PRICE_MISMATCH
                            ? DEBIT_NOTE_SOURCE.PRICE_MISMATCH_AND_REJECTED
                            : DEBIT_NOTE_SOURCE.REJECTED
                        : DEBIT_NOTE_SOURCE.REJECTED,
                    qty: debitNote.rejectedQty,
                    actualPrice,
                    purchasedPrice,
                    note: debitNote.rejectedReason,
                    createdById: payload.createdById,
                    is_manual: true,
                    userId: payload.createdById,
                });

                // Prepare rejection data
                rejectionData.push({
                    rawMaterialId: debitNote.rawMaterialId,
                    purchaseInvoiceId: purchaseInvoice.dataValues.id,
                    totalQty: rawMaterialStockIn!.dataValues.qty,
                    rejectedQty: debitNote.rejectedQty,
                    rejectionReason: debitNote.rejectedReason?.trim() || null,
                    rejectedById: payload.createdById,
                    createdById: payload.createdById,
                });
            }

            // Bulk insert debit notes
            if (debitNotesToCreate.length > 0) {
                const createdDebitNotes = await DebitNoteTable.bulkCreate(debitNotesToCreate, {
                    transaction,
                    userId: payload.createdById,
                    returning: true,
                });
                responseData = createdDebitNotes[0];
            }

            // Bulk insert rejections
            if (rejectionData.length > 0) {
                await RawMaterialRejectionTable.bulkCreate(rejectionData, {
                    transaction,
                    individualHooks: true,
                    userId: payload.createdById,
                });
            }

            return {
                success: true,
                data: responseData,
                message: "Debit Note Created Successfully",
            };
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }




    async updateStock(rawMaterialStockIn: RawMaterialStockInTable, rawMaterialStock: RawMaterialStockTable, rawMaterialId: number, qty: number, purchaseInvoice: PurchaseInvoiceTable, transaction: Transaction) {

        try {
            if (rawMaterialStockIn) {
                if (rawMaterialStockIn?.dataValues.qty >= qty) {

                    if (rawMaterialStockIn?.dataValues.storageLocationId != null) {
                        if (rawMaterialStock) {
                            if (rawMaterialStock?.dataValues?.usableStock < qty) {
                                return HelperMethods.getErrorResponse("Usable stock is less than rejected quantity");
                            }

                            await rawMaterialStock.decrement(["usableStock", "totalStock"], { by: qty, transaction });

                            // Check if usable stock became negative
                            const updatedStock = await RawMaterialStockTable.findOne({
                                where: {
                                    rawMaterialId: rawMaterialId
                                },
                                transaction: transaction
                            });

                            if (updatedStock && updatedStock.dataValues.usableStock < 0) {
                                throw new Error('Operation would result in negative usable stock');
                            }
                            else if (updatedStock && updatedStock.dataValues.totalStock < 0) {
                                throw new Error('Operation would result in negative total stock');
                            }


                            return {
                                success: true,
                                data: null,
                                message: "Stock updated successfully"
                            }
                        }

                    } else {
                        await RawMaterialStockTable.decrement("totalStock", {
                            by: qty,
                            where: { rawMaterialId: rawMaterialId },
                            transaction
                        });
                        const updatedStock = await RawMaterialStockTable.findOne({
                            where: {
                                rawMaterialId: rawMaterialId
                            },
                            transaction: transaction
                        });

                        if (updatedStock && updatedStock.dataValues.totalStock < 0) {
                            throw new Error('Operation would result in negative total stock');
                        }
                        return {
                            success: true,
                            data: null,
                            message: "Stock updated successfully"
                        }
                    }
                } else {
                    return {
                        success: false,
                        data: null,
                        message: "Raw Material Stock is less than Rejected Quantity"
                    }
                }
            }
            else {
                return {
                    success: false,
                    data: null,
                    message: "Raw Material Stock In not found"
                }
            }
        }
        catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Already exists');
            }
            const msg = HelperMethods.isError(error) ? error.message : 'Somthing went wrong : Stock not update'
            return HelperMethods.getErrorResponse(msg);
        }
    }


    async updateDebitNotes(id: number, payload: IUpdateDebitNote, transaction: Transaction): Promise<APIBaseResponse<DebitNoteTable | null>> {
        try {
            // Find the debit note
            const debitNote = await DebitNoteTable.findByPk(id, { transaction });
            if (!debitNote) {
                throw new Error("Debit Note not found");
            }

            // Find the purchase invoice
            const purchaseInvoice = await PurchaseInvoiceTable.findByPk(payload.purchaseInvoiceId, { transaction });
            if (!purchaseInvoice) {
                throw new Error("Purchase Invoice not found");
            }

            // Adjust stock if quantity is updated
            if (debitNote.dataValues.qty > payload.qty) {
                const difference: number = debitNote.dataValues.qty - payload.qty;
                const stockUpdateResult = await this.editStock(payload.rawMaterialId, difference, purchaseInvoice, transaction);

                if (!stockUpdateResult.success) {
                    return stockUpdateResult;
                }
            } else if (debitNote.dataValues.qty < payload.qty) {
                const difference: number = payload.qty - debitNote.dataValues.qty;
                const stockUpdateResult = await this.editStock(payload.rawMaterialId, difference, purchaseInvoice, transaction, true);

                if (!stockUpdateResult.success) {
                    return stockUpdateResult;
                }
            }

            const [affectedRows, updatedRecords] = await DebitNoteTable.update(
                { ...payload, updatedById: payload.updatedById },
                {
                    where: {
                        id
                    },
                    userId: payload.updatedById,
                    individualHooks: true,
                    returning: true,
                    transaction
                }
            );
            return HelperMethods.getSuccessResponse(updatedRecords[0]);
        } catch (error) {
            HelperMethods.handleError(error);

            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse("Category already exists");
            }

            const msg = HelperMethods.isError(error) ? error.message : 'Somthing went wrong : Debit notes not update'
            return HelperMethods.getErrorResponse(msg);
        }
    }

    async editStock(
        rawMaterialId: number,
        qty: number,
        purchaseInvoice: PurchaseInvoiceTable,
        transaction: Transaction,
        increment = false
    ) {
        try {
            const rawMaterialStockIn = await RawMaterialStockInTable.findOne({
                where: {
                    purchaseInvoiceId: purchaseInvoice.dataValues.id,
                    rawMaterialId: rawMaterialId,
                    supplierId: purchaseInvoice.dataValues.supplierId,
                },
                transaction,
            });

            if (!rawMaterialStockIn) {
                return HelperMethods.getErrorResponse("Raw Material Stock In not found");
            }

            if (increment) {
                if (rawMaterialStockIn.dataValues.storageLocationId != null) {
                    const rawMaterialStock = await RawMaterialStockTable.findOne({
                        where: { rawMaterialId: rawMaterialId },
                        transaction,
                    });

                    if (!rawMaterialStock) {
                        return HelperMethods.getErrorResponse("Raw Material Stock not found");
                    }

                    await rawMaterialStock.increment(["usableStock", "totalStock"], { by: qty, transaction });
                } else {
                    await RawMaterialStockTable.increment("totalStock", {
                        by: qty,
                        where: { rawMaterialId: rawMaterialId },
                        transaction,
                    });
                }

                return {
                    success: true,
                    data: null,
                    message: "Stock incremented successfully",
                };
            } else {
                if (rawMaterialStockIn.dataValues.qty < qty) {
                    return HelperMethods.getErrorResponse("Raw Material Stock is less than Rejected Quantity");
                }
                if (rawMaterialStockIn.dataValues.storageLocationId != null) {
                    const rawMaterialStock = await RawMaterialStockTable.findOne({
                        where: { rawMaterialId: rawMaterialId },
                        transaction,
                    });

                    if (!rawMaterialStock) {
                        return HelperMethods.getErrorResponse("Raw Material Stock not found");
                    }

                    if (rawMaterialStock.dataValues.usableStock < qty) {
                        return HelperMethods.getErrorResponse("Usable stock is less than rejected quantity");
                    }

                    await rawMaterialStock.decrement("usableStock", { by: qty, transaction });
                    await rawMaterialStock.decrement("totalStock", { by: qty, transaction });
                    const updatedStock = await RawMaterialStockTable.findOne({
                        where: {
                            rawMaterialId: rawMaterialId
                        },
                        transaction: transaction
                    });

                    if (updatedStock && updatedStock.dataValues.usableStock < 0) {
                        return HelperMethods.getErrorResponse('Operation would result in negative usable stock');
                    }
                    else if (updatedStock && updatedStock.dataValues.totalStock < 0) {
                        return HelperMethods.getErrorResponse('Operation would result in negative total stock');
                    }
                } else {
                    await RawMaterialStockTable.decrement("totalStock", {
                        by: qty,
                        where: { rawMaterialId: rawMaterialId },
                        transaction,
                    });
                    const updatedStock = await RawMaterialStockTable.findOne({
                        where: {
                            rawMaterialId: rawMaterialId
                        },
                        transaction: transaction
                    });

                    if (updatedStock && updatedStock.dataValues.totalStock < 0) {
                        return HelperMethods.getErrorResponse('Operation would result in negative total stock');
                    }
                }

                return {
                    success: true,
                    data: null,
                    message: "Stock decremented successfully",
                };
            }
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse("Already exists");
            }
            return HelperMethods.getErrorResponse();
        }
    }


}