import { DataTypes, Model } from "sequelize";
import { BillOfMaterialRawMaterialTableAttributes, ICreateBillOfMaterialRawMaterialTableAttributes } from "../models/BillOfMaterial";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";
import { RawMaterialVariationTable } from "../../raw_material/database/RawMaterialVariationTable";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { BillOfMaterialTable } from "./BillOfMaterialTable";

class BillOfMaterialRawMaterialTable extends Model<BillOfMaterialRawMaterialTableAttributes, ICreateBillOfMaterialRawMaterialTableAttributes> {

    static initModel() {
        BillOfMaterialRawMaterialTable.init({
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            bomId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            rawMaterialVariationId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            qty: {
                type: DataTypes.FLOAT,
                allowNull: false,
            },
            createdBy: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            updatedBy: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            deletedBy: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: "created_at",
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: "updated_at",
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
                defaultValue: null,
                field: "deleted_at",
            },
        }, {
            sequelize: sequelizeInit,
            tableName: 'bill_of_material_raw_materials',
            paranoid: true,
        });

        return BillOfMaterialRawMaterialTable
    }

    static associate = () => {
        BillOfMaterialRawMaterialTable.belongsTo(CoreUserTable, {
            as: 'createdByUser',
            foreignKey: 'createdBy'
        })
        BillOfMaterialRawMaterialTable.belongsTo(CoreUserTable, {
            as: 'updatedByUser',
            foreignKey: 'updatedBy'
        })
        BillOfMaterialRawMaterialTable.belongsTo(CoreUserTable, {
            as: 'deletedByUser',
            foreignKey: 'deletedBy'
        })
        RawMaterialVariationTable.hasMany(BillOfMaterialRawMaterialTable, {
            as: 'billofmaterial_rawmaterial',
            foreignKey: 'rawMaterialVariationId'
        })
        BillOfMaterialRawMaterialTable.belongsTo(RawMaterialVariationTable, {
            as: 'rawMaterialVariation',
            foreignKey: 'rawMaterialVariationId'
        })
        BillOfMaterialRawMaterialTable.belongsTo(BillOfMaterialTable, {
            as: 'billOfMaterial',
            foreignKey: 'bomId'
        });
    }

    static hooks = () => {

        BillOfMaterialRawMaterialTable.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "BillOfMaterialRawMaterial",
                instance,
                options
            );
        });

        BillOfMaterialRawMaterialTable.addHook("afterUpdate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "update",
                "BillOfMaterialRawMaterial",
                instance,
                options
            );
        });

        BillOfMaterialRawMaterialTable.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "BillOfMaterialRawMaterial",
                instance,
                options
            );
        });

    }


}


export { BillOfMaterialRawMaterialTable };