import { DataTypes, Model } from "sequelize";
import { BillOfMaterialAttributes, ICreateBillOfMaterialAttributes } from "../models/BillOfMaterial";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { FinalGoodsTable } from "../../final-goods/model/FgTable";
import { FinalGoodsVariationTable } from "../../final-goods/model/FgVariationTable";
import { BillOfMaterialRawMaterialTable } from "./BillOfMaterialRawMaterialTable";

class BillOfMaterialTable extends Model<BillOfMaterialAttributes, ICreateBillOfMaterialAttributes> {

    static initModel() {

        BillOfMaterialTable.init({
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            finalGoodsVariationId: {
                type: DataTypes.INTEGER,
                allowNull: false,
                unique: true,
            },
            createdBy: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            updatedBy: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            deletedBy: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: "created_at",
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: "updated_at",
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
                defaultValue: null,
                field: "deleted_at",
            },
        }, {
            sequelize: sequelizeInit,
            tableName: 'bill_of_materials',
            timestamps: true,
            paranoid: true,
            // underscored: true,
        })
        return BillOfMaterialTable
    }

    static associate = () => {
        BillOfMaterialTable.belongsTo(CoreUserTable, {
            as: 'createdByUser',
            foreignKey: 'createdBy'
        })
        BillOfMaterialTable.belongsTo(CoreUserTable, {
            as: 'updatedByUser',
            foreignKey: 'updatedBy'
        })
        BillOfMaterialTable.belongsTo(CoreUserTable, {
            as: 'deletedByUser',
            foreignKey: 'deletedBy'
        })
        BillOfMaterialTable.belongsTo(FinalGoodsVariationTable, {
            as: 'finalGoodsVariation',
            foreignKey: 'finalGoodsVariationId'
        })
        BillOfMaterialTable.hasMany(BillOfMaterialRawMaterialTable, {
            as: 'rawMaterialVariation',
            foreignKey: 'bomId'
        });
    }

    static hooks = () => {

        BillOfMaterialTable.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "BillOfMaterial",
                instance,
                options
            );
        });

        BillOfMaterialTable.addHook("afterUpdate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "update",
                "BillOfMaterial",
                instance,
                options
            );
        });

        BillOfMaterialTable.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "BillOfMaterial",
                instance,
                options
            );
        });
    }

    public static getInclude() {
        return [
            {
                model: FinalGoodsVariationTable,
                as: "finalGoodsVariation",
                include: [
                    { as: 'createdByUser', model: CoreUserTable },
                    { as: 'updatedByUser', model: CoreUserTable },
                    { as: 'deletedByUser', model: CoreUserTable },
                ]
            }
        ];
    }
}



export { BillOfMaterialTable }