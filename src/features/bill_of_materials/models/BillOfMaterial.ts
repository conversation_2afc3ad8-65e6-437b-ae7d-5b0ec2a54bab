import { Optional } from "sequelize";
import { <PERSON>Meta, Meta<PERSON>ser, NewBaseMeta, ParsedMeta } from "../../../core/CoreInterfaces";

import {  IRawMaterialVariationWithAvgPriceResponse } from "../../raw_material/models/IRawMaterialAndVariations";
import { ParsedFGVariationBase } from "../../final-goods/interface/IFinalGoodsVariation";

interface BillOfMaterialAttributes extends BaseMeta {
    finalGoodsVariationId: number;
}

interface ICreateBillOfMaterialAttributes extends Optional<BillOfMaterialAttributes, "id" | "createdAt" | "updatedAt" | "deletedAt"> { }


interface BillOfMaterialRawMaterialTableAttributes extends BaseMeta {
    bomId: number;
    rawMaterialVariationId: number;
    qty: number;
}

interface ICreateBillOfMaterialRawMaterialTableAttributes extends Optional<BillOfMaterialRawMaterialTableAttributes, "id" | "createdAt" | "updatedAt" | "deletedAt"> { }


interface BillOfMaterialRawMaterialPayload {
    bomId: number;
    rawMaterialVariationId: number;
    qty: number;
}

interface BillOfMaterialPayload {
    finalGoodsVariationId: number;
    rawMaterials: BillOfMaterialRawMaterialPayload[];
}

interface BillOfMaterialUpdatePayload extends Omit<BillOfMaterialPayload,'finalGoodsId'>{}

interface ParsedBillOfMaterialRawMaterial extends ParsedMeta {
    id: number;
    bomId: number;
    rawMaterialVariationId: number;
    qty: number;
}
interface rawMaterialVariation {
    id: number,
    qty: number,
    name: string
}
interface ParsedBillOfMaterial extends ParsedMeta {
    id: number;
    finalGoodsVariation: ParsedFGVariationBase;
    rawMaterialVariation: rawMaterialVariation[]
}
interface BomList  {
    id: number;
    fgVariation: ParsedFGVariationBase;
    c_by: MetaUser;
    c_at: Date;
} 


interface IBomGetSingleResponse extends NewBaseMeta {
     id: number;
    fgVariation: any;
    rmVariations: IBillOfMaterialRawMaterialVariation[];
}

interface IBillOfMaterialRawMaterialVariation {
    id: number;
    bomId: number;
    rawMaterialVariation: IRawMaterialVariationWithAvgPriceResponse;
    qty: number;

}


export {
    BillOfMaterialAttributes,
    ICreateBillOfMaterialAttributes,
    BillOfMaterialRawMaterialTableAttributes,
    ICreateBillOfMaterialRawMaterialTableAttributes,
    BillOfMaterialPayload,
    ParsedBillOfMaterial,
    ParsedBillOfMaterialRawMaterial,
    IBomGetSingleResponse,
    BillOfMaterialUpdatePayload,
    BomList
};