import * as express from 'express';
import { BarcodeController } from '../controllers/BarcodeController';

const barcodeRouter = express.Router();
const apiInitialPath = "/barcode";

barcodeRouter.post(`${apiInitialPath}/create`, BarcodeController.createBarcode);
barcodeRouter.put(`${apiInitialPath}/update/:id`, BarcodeController.updateBarcode);
barcodeRouter.get(`${apiInitialPath}/:id`, BarcodeController.getBarcodeById);
barcodeRouter.get(`${apiInitialPath}/list`, BarcodeController.getAllBarcodes);
barcodeRouter.delete(`${apiInitialPath}/delete/:id`, BarcodeController.deleteBarcode);

export { barcodeRouter };