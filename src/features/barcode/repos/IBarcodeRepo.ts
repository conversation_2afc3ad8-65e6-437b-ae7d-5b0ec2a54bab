import { Transaction } from "sequelize";
import { DTO } from "../../../core/DTO";
import { PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { BarcodeCreatePayload, BarcodeUpdatePayload } from "../validations/BarcodeValidationSchema";
import { ParsedBarcode } from "../interface/BarcodeInterface";

interface IBarcodeRepo {
    createBarcode(payload: BarcodeCreatePayload, userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode>>;
    updateBarcode(id: number, payload: BarcodeUpdatePayload, userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode>>;
    getBarcodeById(id: number, userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode>>;
    getAllBarcodes(page: number, limit: number, userId: number, transaction: Transaction): Promise<DTO<PaginatedBaseResponse<ParsedBarcode>>>;
    deleteBarcode(id: number, userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode | null>>;
    bulkCreateBarcode(payload: BarcodeCreatePayload[], userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode[]>>
    getByStockInId(id:number,transaction:Transaction):Promise<DTO<ParsedBarcode[]>>;
}

export { IBarcodeRepo };