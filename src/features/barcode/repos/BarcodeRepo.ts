import { Transaction } from "sequelize";
import { IBarcodeRepo } from "./IBarcodeRepo";
import { DTO } from "../../../core/DTO";
import { BarcodeMapModel } from "../model/BarcodeMapModel";
import { PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { BarcodeCreatePayload, BarcodeUpdatePayload } from "../validations/BarcodeValidationSchema";
import { PaginationProvider } from "../../pagination/PaginationProvider";
import { parseBarcode } from "../parser/BarcodeParser";
import { ParsedBarcode } from "../interface/BarcodeInterface";

class BarcodeRepo implements IBarcodeRepo {
    async getByStockInId(id: number, transaction: Transaction): Promise<DTO<ParsedBarcode[]>> {
        try {
            const barcodes = await BarcodeMapModel.findAll({ where: { stock_in_id: id }, transaction,include:BarcodeMapModel.getInclude() });
            if (!barcodes) {
                return DTO.HandledError(`Barcode with  stock in id ${id} not found`);
            } if (barcodes.length === 0) {
                return DTO.Success([])
            }
            return DTO.Success(barcodes.map((bar)=>parseBarcode(bar.toJSON())));
        } catch (error) {
            return HelperMethods.handleError(error);
        }
    }



    async createBarcode(payload: BarcodeCreatePayload, userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode>> {
        try {
            const barcode = await BarcodeMapModel.create({
                v_id: payload.v_id,
                barcode: payload.barcode,
                stock_in_id: payload.stock_in_id,
                createdBy: userId,
            }, { transaction, userId: userId });


            const exists = await this.getBarcodeById(barcode.toJSON().id, userId, transaction)
            if (!exists.success) return exists

            return DTO.Success(exists.data);
        } catch (error) {
            return HelperMethods.handleError(error);
        }
    }

    async updateBarcode(id: number, payload: BarcodeUpdatePayload, userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode>> {
        try {
            const barcode = await BarcodeMapModel.findByPk(id, { transaction });
            if (!barcode) {
                return DTO.HandledError(`Barcode with id ${id} not found`);
            }

            barcode.v_id = payload.v_id;
            barcode.stock_in_id = payload.stock_in_id;
            barcode.barcode = payload.barcode;
            barcode.updatedBy = userId;

            await barcode.save({ transaction });

            const exists = await this.getBarcodeById(barcode.toJSON().id, userId, transaction)
            if (!exists.success) return exists

            return DTO.Success(exists.data);
        } catch (error) {
            return HelperMethods.handleError(error);
        }
    }

    async getBarcodeById(id: number, userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode>> {
        try {
            const barcode = await BarcodeMapModel.findByPk(id, { include: BarcodeMapModel.getInclude(), transaction });
            if (!barcode) {
                return DTO.HandledError(`Barcode with id ${id} not found`);
            }
            return DTO.Success(parseBarcode(barcode.toJSON()));
        } catch (error) {
            return HelperMethods.handleError(error);
        }
    }

    async getAllBarcodes(page: number, limit: number, userId: number, transaction: Transaction): Promise<DTO<PaginatedBaseResponse<ParsedBarcode>>> {
        try {
            const paginatedData = await new PaginationProvider<any, ParsedBarcode>().getPaginatedRecords(BarcodeMapModel,
                { limit: limit, page: page, where: null, dateColumn: "createdAt" }, transaction, parseBarcode)


            return DTO.Success({
                totalData: paginatedData.total,
                data: paginatedData.rows,
                totalPages: paginatedData.totalPages,
                currentPage: paginatedData.currentPage, // Calculate current page
            })
        } catch (error) {
            return HelperMethods.handleError(error);
        }
    }

    async deleteBarcode(id: number, userId: number, transaction: Transaction): Promise<DTO<null>> {
        try {
            const barcode = await BarcodeMapModel.findByPk(id, { transaction });
            if (!barcode) {
                return DTO.HandledError(`Barcode with id ${id} not found`);
            }

            barcode.deletedBy = userId;
            barcode.deletedAt = new Date();
            await barcode.save({ transaction });

            return DTO.Success(null);
        } catch (error) {
            return HelperMethods.handleError(error);
        }
    }


    async bulkCreateBarcode(payload: BarcodeCreatePayload[], userId: number, transaction: Transaction): Promise<DTO<ParsedBarcode[]>> {
        try {
            const transformPayload = payload.map((data) => {
                return {
                    v_id: data.v_id,
                    barcode: data.barcode,
                    stock_in_id: data.stock_in_id,
                    createdBy: userId,
                }
            })

            for (const barcode of payload) {
                const existsBarcode = await BarcodeMapModel.findOne({
                    where: {
                        barcode: barcode.barcode
                    },
                    transaction
                })

                if (existsBarcode) {
                    return DTO.HandledError(`The barcode '${barcode.barcode}' is already assigned to another item.`)
                }
            }

            const createdBarcodes = await BarcodeMapModel.bulkCreate(transformPayload, { transaction, userId: userId });
            const parsedData: ParsedBarcode[] = []

            for (const barcode of createdBarcodes) {
                const exists = await this.getBarcodeById(barcode.toJSON().id, userId, transaction)
                if (!exists.success) return exists
                parsedData.push(exists.data)
            }

            return DTO.Success(parsedData);
        } catch (error) {
            return HelperMethods.handleError(error);
        }
    }
}

export { BarcodeRepo };