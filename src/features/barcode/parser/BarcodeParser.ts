import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser";
import { ParsedBarcode } from "../interface/BarcodeInterface";

const parseBarcode = (data: any): ParsedBarcode => {

    const parsedData: ParsedBarcode = {
        id: data.id,
        // variation: parsedFgVariationBase(data.fgVariation),
        // stockIn: parsedFGStockInParsed(data.stockIn),
        barcode: data.barcode,
        createdBy: parseUserToMetaUser(data.createdByUser),
        updatedBy: data.updatedByUser ? parseUserToMetaUser(data.updatedByUser) : null,
        deletedBy: data.deletedByUser ? parseUserToMetaUser(data.deletedByUser) : null,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        deletedAt: data.deletedAt,
        logs: data.logs
    }  
    
    return parsedData;

}

export { parseBarcode };