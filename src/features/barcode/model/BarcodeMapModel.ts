import { DataTypes, Includeable, Model } from "sequelize";
import { BarcodeAttributes, ICreateBarcodeAttributes } from "../interface/BarcodeInterface";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";
import { FinalGoodsVariationTable } from "../../final-goods/model/FgVariationTable";
import { FGStockInModel } from "../../fg-stock-in/model/FgStockInModel";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { LogsTable } from "../../logs/database/LogsTable";
import { finalGoodsVariationsInclude } from "../../final-goods/includes/fg-include";

class BarcodeMapModel extends Model<BarcodeAttributes, ICreateBarcodeAttributes> implements BarcodeAttributes {
    public id!: number;
    public v_id!: number;
    public stock_in_id!: number;
    public barcode!: string | null;
    public createdBy!: number;
    public updatedBy!: number | null;
    public deletedBy!: number | null;
    public createdAt!: Date;
    public updatedAt!: Date | null;
    public deletedAt!: Date | null;


    public static getInclude():Includeable[] {
        const include:Includeable[] = [
            {
                as:'fgVariation',
                model:FinalGoodsVariationTable,
                include:finalGoodsVariationsInclude()
            },
            {
                as:'stockIn',
                model:FGStockInModel
            },
            {
                as:'createdByUser',
                model:CoreUserTable
            },
            {
                as:'updatedByUser',
                model:CoreUserTable
            },
            {
                as:'deletedByUser',
                model:CoreUserTable
            },
        ]

        return include
    }

    public static associate() {

        BarcodeMapModel.belongsTo(FinalGoodsVariationTable, {
            foreignKey: 'v_id',
            as: 'fgVariation',
        });

        BarcodeMapModel.belongsTo(FGStockInModel, {
            foreignKey: 'stock_in_id',
            as: 'stockIn',
        });

        BarcodeMapModel.belongsTo(CoreUserTable, {
            foreignKey: 'createdBy',
            as: 'createdByUser',
        });

        BarcodeMapModel.belongsTo(CoreUserTable, {
            foreignKey: 'updatedBy',
            as: 'updatedByUser',
        });

        BarcodeMapModel.belongsTo(CoreUserTable, {
            foreignKey: 'deletedBy',
            as: 'deletedByUser',
        });

        BarcodeMapModel.hasMany(LogsTable, {
            foreignKey: "recordId",
            sourceKey: "id",
            constraints: false,
            as: "logs",
        });
    }

    public static initModel() {
        BarcodeMapModel.init({
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            v_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            stock_in_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            barcode: {
                type: DataTypes.STRING,
                allowNull: true,
                unique:true,
            },
            createdBy: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            updatedBy: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            deletedBy: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: true,
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
            }
        }, {
            sequelize: sequelizeInit, // This should be your Sequelize instance
            tableName: "barcode_map",
            timestamps: true,
            paranoid: true, // Enables soft deletes
        })
        return BarcodeMapModel;
    }

    public static hooks() {
        BarcodeMapModel.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "BarcodeMap",
                instance,
                options,
            );
        });

        BarcodeMapModel.addHook("afterUpdate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "update",
                "BarcodeMap",
                instance,
                options,
            );
        });

        BarcodeMapModel.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "BarcodeMap",
                instance,
                options,
            );
        });
    }
}

export { BarcodeMapModel };