import { Optional } from "sequelize";
import { BaseMeta, NewParsedMeta, ParsedMeta } from "../../../core/CoreInterfaces";

interface BarcodeAttributes extends BaseMeta {
    v_id: number;
    stock_in_id: number;
    barcode: string | null;
}

interface ICreateBarcodeAttributes extends Optional<BarcodeAttributes, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'> {}

interface ParsedBarcode extends ParsedMeta {
    id: number;
    // variation: ParsedFGVariationBase;
    // stockIn: ParsedStockIn;
    barcode: string;
    logs: any[];
}

export { BarcodeAttributes, ICreateBarcodeAttributes, ParsedBarcode };

