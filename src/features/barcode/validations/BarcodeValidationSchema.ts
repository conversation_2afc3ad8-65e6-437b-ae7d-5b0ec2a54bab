import { z } from "zod";

class BarcodeValidationSchema {

    static createBarcodeSchema = z.object({
        v_id: z.number({
            required_error: "Variation ID is required",
            invalid_type_error: "Variation ID must be a number"
        }).int().positive(),
        stock_in_id: z.number({
            required_error: "Stock In ID is required",
            invalid_type_error: "Stock In ID must be a number"
        }).int().positive(),
        barcode: z.string({
            required_error: "Barcode is required",
            invalid_type_error: "Barcode must be a string"
        }).min(1, "Barcode cannot be empty")
    })

    static updateBarcodeSchema = z.object({
        id: z.number({
            required_error: "ID is required",
            invalid_type_error: "ID must be a number"
        }).int().positive(),
        v_id: z.number({
            required_error: "Variation ID is required",
            invalid_type_error: "Variation ID must be a number"
        }).int().positive(),
        stock_in_id: z.number({
            required_error: "Stock In ID is required",
            invalid_type_error: "Stock In ID must be a number"
        }).int().positive(),
        barcode: z.string({
            required_error: "Barcode is required",
            invalid_type_error: "Barcode must be a string"
        }).min(1, "Barcode cannot be empty")
    })

    static getBarcodeByIdSchema = z.object({
        id: z.number({
            required_error: "ID is required",
            invalid_type_error: "ID must be a number"
        }).int().positive(),
    })

    static getAllBarcodesSchema = z.object({
        page: z.number().optional(),
        pageSize: z.number().optional(),
        search: z.string().optional(),
        order: z.enum(['ASC', 'DESC']).optional()
    })

    static deleteBarcodeSchema = z.object({
        id: z.number({
            required_error: "ID is required",
            invalid_type_error: "ID must be a number"
        }).int().positive(),
    })

}

type BarcodeCreatePayload = z.infer<typeof BarcodeValidationSchema.createBarcodeSchema>;
type BarcodeUpdatePayload = z.infer<typeof BarcodeValidationSchema.updateBarcodeSchema>;
type BarcodeGetByIdPayload = z.infer<typeof BarcodeValidationSchema.getBarcodeByIdSchema>;
type BarcodeGetAllPayload = z.infer<typeof BarcodeValidationSchema.getAllBarcodesSchema>;

export { BarcodeValidationSchema, BarcodeCreatePayload, BarcodeUpdatePayload, BarcodeGetByIdPayload, BarcodeGetAllPayload };