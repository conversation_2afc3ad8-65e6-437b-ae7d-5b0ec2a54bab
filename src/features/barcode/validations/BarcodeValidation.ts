
import { Request } from "express";
import { DTO } from "../../../core/DTO";
import { extractAllZodErrors } from "../../../core/utils";
import { BarcodeCreatePayload, BarcodeUpdatePayload, BarcodeValidationSchema } from "./BarcodeValidationSchema";

class BarcodeValidation {

    static create = (req: Request): DTO<BarcodeCreatePayload, any> => {
        const payload = req.body;
        const result = BarcodeValidationSchema.createBarcodeSchema.safeParse(req.body);

        if (!result.success) {
            return DTO.HandledError("Validation Error", 422, extractAllZodErrors(result.error));
        }

        return DTO.Success(payload);
    }

    static update = (req: Request): DTO<BarcodeUpdatePayload, any> => {
        const payload = req.body;
        const result = BarcodeValidationSchema.updateBarcodeSchema.safeParse(req.body);

        if (!result.success) {
            return DTO.HandledError("Validation Error", 422, extractAllZodErrors(result.error));
        }

        return DTO.Success(payload);
    }

    static getById = (req: Request): DTO<{ id: number }, any> => {
        const result = BarcodeValidationSchema.getBarcodeByIdSchema.safeParse(req.params);

        if (!result.success) {
            return DTO.HandledError("Validation Error", 422, extractAllZodErrors(result.error));
        }

        return DTO.Success({ id: result.data.id });
    }

    static getAll = (req: Request): DTO<any, any> => {
        const result = BarcodeValidationSchema.getAllBarcodesSchema.safeParse(req.query);

        if (!result.success) {
            return DTO.HandledError("Validation Error", 422, extractAllZodErrors(result.error));
        }

        return DTO.Success({
            page: result.data.page,
            pageSize: result.data.pageSize,
            search: result.data.search,
            order: result.data.order
        });
    }

    static delete = (req: Request): DTO<{ id: number }, any> => {
        const result = BarcodeValidationSchema.deleteBarcodeSchema.safeParse(req.params);

        if (!result.success) {
            return DTO.HandledError("Validation Error", 422, extractAllZodErrors(result.error));
        }

        return DTO.Success({ id: result.data.id });
    }

}

export { BarcodeValidation };