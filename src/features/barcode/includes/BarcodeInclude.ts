import { fgStockInInclude } from "../../fg-stock-in/includes/FGStockInInclude"
import { FGStockInModel } from "../../fg-stock-in/model/FgStockInModel"
import { finalGoodsVariationsInclude } from "../../final-goods/includes/fg-include"
import { FinalGoodsVariationTable } from "../../final-goods/model/FgVariationTable"
import { LogsTable } from "../../logs/database/LogsTable"
import { CoreUserTable } from "../../users/core/database/CoreUserTable"

const barcodeInclude = () => {
    const include = [
        {
            model: FinalGoodsVariationTable,
            as: "fgVariation",
            include: finalGoodsVariationsInclude()
        },
        {
            model: FGStockInModel,
            as: "stockIn",
            include: fgStockInInclude()
        },
        {
            model: CoreUserTable,
            as: "createdByUser",
        },
        {
            model: CoreUserTable,
            as: "updatedByUser",
        },
        {
            model: CoreUserTable,
            as: "deletedByUser",
        },
        {
            model: LogsTable,
            as: "logs",
        }
    ]

    return include
}

export { barcodeInclude }