import { Request, Response } from "express";
import { genericController } from "../../../sequelize/utils/sequelize-utils";
import { BarcodeValidation } from "../validations/BarcodeValidation";
import { RepoProvider } from "../../../core/RepoProvider";
import { get } from "lodash";
import { ERROR_MESSAGE } from "../../../core/constants";

class BarcodeController {

    static async createBarcode(req: Request, res: Response) {
        genericController(req, res, {
            validate: (req) => {
                const validationRes = BarcodeValidation.create(req);
                if (!validationRes.success) {
                    return validationRes
                }
                return { success: true, data: validationRes.data }
            },
            logic: async (ctx) => {
                const payload = ctx.payload;
                if (!payload) {
                    throw {
                        code: 400,
                        message: 'Request body is required',
                        error: new Error('Request body is required'),
                    }
                }
                const response = await RepoProvider.barcodeRepo.createBarcode(payload, ctx.userId, ctx.transaction);
                if (!response.success) {
                    throw {
                        code: 400,
                        message: response.message,
                        error: response.error,
                    }
                }
                return {
                    message: response,
                    data: response.data,
                };
            }
        })
    }

    static async updateBarcode(req: Request, res: Response) {
        genericController(req, res, {
            validate: (req) => {
                const validationRes = BarcodeValidation.update(req);
                if (!validationRes.success) {
                    return validationRes
                }
                return { success: true, data: validationRes.data }
            },
            logic: async (ctx) => {
                const payload = ctx.payload;
                if (!payload) {
                    throw {
                        code: 400,
                        message: 'Request body is required',
                        error: new Error('Request body is required'),
                    }
                }
                const id = parseInt(get(req.params, 'id')!);
                
                if (!id || isNaN(id)) {
                    throw {
                        code: 400,
                        message: ERROR_MESSAGE.ID_SHOULD_BE_NUMBER,
                        error: new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER),
                    }
                }
                const response = await RepoProvider.barcodeRepo.updateBarcode(id, payload, ctx.userId, ctx.transaction);
                if (!response.success) {
                    throw {
                        code: 400,
                        message: response.message,
                        error: response.error,
                    }
                }
                return {
                    message: response,
                    data: response.data,
                };
            }
        })
    }

    static async getBarcodeById(req: Request, res: Response) {
        genericController(req, res, {
            validate: (req) => {
                const validationRes = BarcodeValidation.getById(req);
                if (!validationRes.success) {
                    return validationRes
                }
                return { success: true, data: validationRes.data }
            },
            logic: async (ctx) => {
                const id = ctx.payload?.id;
                if (!id) {
                    throw {
                        code: 400,
                        message: 'Request body is required',
                        error: new Error('Request body is required'),
                    }
                }
                const response = await RepoProvider.barcodeRepo.getBarcodeById(id, ctx.userId, ctx.transaction);
                if (!response.success) {
                    throw {
                        code: 400,
                        message: response.message,
                        error: response.error,
                    }
                }
                return {
                    message: response,
                    data: response.data,
                };
            }
        })
    }

    static async getAllBarcodes(req: Request, res: Response) {
        genericController(req, res, {
            validate: (req) => {
                const validationRes = BarcodeValidation.getAll(req);
                if (!validationRes.success) {
                    return validationRes
                }
                return { success: true, data: validationRes.data }
            },
            logic: async (ctx) => {
                const payload = ctx.payload;
                if (!payload) {
                    throw {
                        code: 400,
                        message: 'Request body is required',
                        error: new Error('Request body is required'),
                    }
                }
                const response = await RepoProvider.barcodeRepo.getAllBarcodes(payload.page, payload.pageSize, ctx.userId, ctx.transaction);
                if (!response.success) {
                    throw {
                        code: 400,
                        message: response.message,
                        error: response.error,
                    }
                }
                return {
                    message: response,
                    data: response.data,
                };
            }
        })
    }

    static async deleteBarcode(req: Request, res: Response) {
        genericController(req, res, {
            validate: (req) => {
                const validationRes = BarcodeValidation.delete(req);
                if (!validationRes.success) {
                    return validationRes
                }
                return { success: true, data: validationRes.data }
            },
            logic: async (ctx) => {
                const id = ctx.payload?.id;
                if (!id) {
                    throw {
                        code: 400,
                        message: 'Request body is required',
                        error: new Error('Request body is required'),
                    }
                }
                const response = await RepoProvider.barcodeRepo.deleteBarcode(id, ctx.userId, ctx.transaction);
                if (!response.success) {
                    throw {
                        code: 400,
                        message: response.message,
                        error: response.error,
                    }
                }
                return {
                    message: response,
                    data: response.data,
                };
            }
        })
    }

}

export { BarcodeController };